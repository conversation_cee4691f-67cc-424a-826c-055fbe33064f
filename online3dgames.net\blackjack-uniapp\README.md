# Blackjack 21 UniApp

基于blackjack-online游戏的UniApp移植版本，适用于发布到Google Play商店。

## 项目结构

```
blackjack-uniapp/
├── manifest.json          # 应用配置
├── pages.json             # 页面路由配置
├── App.vue               # 应用入口
├── pages/
│   ├── index/index.vue   # 主页面
│   ├── game/game.vue     # 游戏页面
│   └── settings/settings.vue # 设置页面
├── static/
│   ├── css/common.css    # 通用样式
│   ├── audio/            # 音频文件
│   └── images/           # 图片资源
└── utils/
    └── gameLogic.js      # 游戏核心逻辑
```

## 核心功能

### ✅ 已完成
- 完整的21点游戏逻辑
- 响应式UI设计
- 游戏状态管理
- 本地数据存储
- 设置系统
- 音效支持框架

### 🔄 需要完善
- 添加音频文件
- 添加图片资源
- 卡牌动画效果
- 筹码飞行动画
- 更多游戏特效

## 技术特点

1. **完全移植原游戏逻辑** - 保持所有原有功能
2. **Vue 3 + UniApp** - 现代化开发框架
3. **响应式设计** - 适配各种屏幕尺寸
4. **原生性能** - 比WebView更流畅
5. **离线游戏** - 无需网络连接

## 开发说明

1. 使用HBuilderX打开项目
2. 复制音频和图片资源到对应目录
3. 运行到手机或模拟器测试
4. 打包发布到应用商店

## Google Play发布优势

- 完整的原生应用体验
- 支持离线游戏
- 可添加推送通知
- 支持应用内购买
- 更好的性能表现
