<template>
	<view class="container">
		<view class="header">
			<text class="title">Blackjack 21</text>
			<text class="subtitle">Professional Card Game</text>
		</view>
		
		<view class="game-stats">
			<view class="stat-item">
				<text class="stat-value">{{ gameStats.balance }}</text>
				<text class="stat-label">Balance</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">{{ gameStats.totalGames }}</text>
				<text class="stat-label">Games</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">{{ gameStats.winRate }}%</text>
				<text class="stat-label">Win Rate</text>
			</view>
		</view>
		
		<view class="menu-buttons">
			<button class="menu-btn primary" @click="startGame">
				<text class="btn-text">Start Game</text>
			</button>
			
			<button class="menu-btn secondary" @click="showSettings">
				<text class="btn-text">Settings</text>
			</button>
		</view>
		
		<view class="footer">
			<text class="version">Version 1.0.0</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			gameStats: {
				balance: 1000,
				totalGames: 0,
				totalWins: 0,
				winRate: 0
			}
		}
	},
	onLoad() {
		this.loadGameStats()
	},
	onShow() {
		this.loadGameStats()
	},
	methods: {
		loadGameStats() {
			try {
				const stats = uni.getStorageSync('blackjack_stats')
				if (stats) {
					this.gameStats = JSON.parse(stats)
					this.gameStats.winRate = this.gameStats.totalGames > 0 
						? Math.round((this.gameStats.totalWins / this.gameStats.totalGames) * 100) 
						: 0
				}
			} catch (e) {
				console.log('Failed to load stats:', e)
			}
		},
		startGame() {
			uni.navigateTo({
				url: '/pages/game/game'
			})
		},
		showSettings() {
			uni.navigateTo({
				url: '/pages/settings/settings'
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #1a4b3a, #2d5a4a);
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.header {
	text-align: center;
	margin-bottom: 80rpx;
}

.title {
	font-size: 60rpx;
	font-weight: bold;
	color: #ffd700;
	margin-bottom: 20rpx;
	display: block;
}

.subtitle {
	font-size: 32rpx;
	color: #ffffff;
	opacity: 0.8;
	display: block;
}

.game-stats {
	display: flex;
	justify-content: space-around;
	width: 100%;
	margin-bottom: 80rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 40rpx 20rpx;
}

.stat-item {
	text-align: center;
	flex: 1;
}

.stat-value {
	font-size: 48rpx;
	font-weight: bold;
	color: #ffd700;
	display: block;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 28rpx;
	color: #ffffff;
	opacity: 0.8;
	display: block;
}

.menu-buttons {
	width: 100%;
	max-width: 500rpx;
}

.menu-btn {
	width: 100%;
	height: 100rpx;
	border-radius: 50rpx;
	border: none;
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.menu-btn.primary {
	background: linear-gradient(135deg, #ffd700, #ffed4e);
	color: #1a4b3a;
}

.menu-btn.secondary {
	background: rgba(255, 255, 255, 0.2);
	color: #ffffff;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.footer {
	margin-top: auto;
	text-align: center;
}

.version {
	font-size: 24rpx;
	color: #ffffff;
	opacity: 0.6;
}
</style>
