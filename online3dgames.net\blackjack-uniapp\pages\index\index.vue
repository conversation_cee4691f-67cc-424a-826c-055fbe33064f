<template>
	<view class="container">
		<view class="header">
			<image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
			<text class="title">Blackjack 21</text>
			<text class="subtitle">Professional Card Game</text>
		</view>
		
		<view class="game-stats">
			<view class="stat-item">
				<text class="stat-value">{{ gameStats.balance }}</text>
				<text class="stat-label">Balance</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">{{ gameStats.totalGames }}</text>
				<text class="stat-label">Games</text>
			</view>
			<view class="stat-item">
				<text class="stat-value">{{ gameStats.winRate }}%</text>
				<text class="stat-label">Win Rate</text>
			</view>
		</view>
		
		<view class="menu-buttons">
			<button class="menu-btn primary" @click="startGame">
				<text class="btn-icon">🎮</text>
				<text class="btn-text">Start Game</text>
			</button>
			
			<button class="menu-btn secondary" @click="showRules">
				<text class="btn-icon">📖</text>
				<text class="btn-text">Game Rules</text>
			</button>
			
			<button class="menu-btn secondary" @click="showSettings">
				<text class="btn-icon">⚙️</text>
				<text class="btn-text">Settings</text>
			</button>
			
			<button class="menu-btn secondary" @click="showAchievements">
				<text class="btn-icon">🏆</text>
				<text class="btn-text">Achievements</text>
			</button>
		</view>
		
		<view class="footer">
			<text class="version">Version 1.0.0</text>
		</view>
		
		<!-- Rules Modal -->
		<view v-if="showRulesModal" class="modal-overlay" @click="hideRulesModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">Blackjack Rules</text>
					<button class="close-btn" @click="hideRulesModal">×</button>
				</view>
				<scroll-view class="modal-body" scroll-y>
					<text class="rule-text">
						• Goal: Get cards totaling 21 or as close as possible without going over
						• Face cards (J, Q, K) are worth 10 points
						• Aces are worth 1 or 11 points (whichever is better)
						• Blackjack (21 with first 2 cards) pays 3:2
						• Dealer must hit on 16 and stand on 17
						• You can double down on any two cards
						• Split pairs to play two hands
						• Insurance available when dealer shows Ace
						• Surrender available on first two cards
					</text>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			showRulesModal: false,
			gameStats: {
				balance: 1000,
				totalGames: 0,
				totalWins: 0,
				winRate: 0
			}
		}
	},
	onLoad() {
		this.loadGameStats()
	},
	onShow() {
		this.loadGameStats()
	},
	methods: {
		loadGameStats() {
			try {
				const stats = uni.getStorageSync('blackjack_stats')
				if (stats) {
					this.gameStats = JSON.parse(stats)
					this.gameStats.winRate = this.gameStats.totalGames > 0 
						? Math.round((this.gameStats.totalWins / this.gameStats.totalGames) * 100) 
						: 0
				}
			} catch (e) {
				console.log('Failed to load stats:', e)
			}
		},
		startGame() {
			uni.navigateTo({
				url: '/pages/game/game'
			})
		},
		showRules() {
			this.showRulesModal = true
		},
		hideRulesModal() {
			this.showRulesModal = false
		},
		showSettings() {
			uni.navigateTo({
				url: '/pages/settings/settings'
			})
		},
		showAchievements() {
			uni.showToast({
				title: 'Coming Soon',
				icon: 'none'
			})
		}
	}
}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background: linear-gradient(135deg, #1a4b3a 0%, #2d5a4a 100%);
	padding: 40rpx;
}

.header {
	text-align: center;
	margin-bottom: 80rpx;
}

.logo {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 40rpx;
}

.title {
	display: block;
	font-size: 60rpx;
	font-weight: bold;
	color: #ffd700;
	margin-bottom: 20rpx;
}

.subtitle {
	display: block;
	font-size: 32rpx;
	color: #ffffff;
	opacity: 0.8;
}

.game-stats {
	display: flex;
	justify-content: space-around;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 80rpx;
}

.stat-item {
	text-align: center;
}

.stat-value {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #ffd700;
	margin-bottom: 10rpx;
}

.stat-label {
	display: block;
	font-size: 28rpx;
	color: #ffffff;
	opacity: 0.8;
}

.menu-buttons {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.menu-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 120rpx;
	border-radius: 20rpx;
	border: none;
	font-size: 36rpx;
	font-weight: bold;
	gap: 20rpx;
}

.menu-btn.primary {
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	color: #1a4b3a;
}

.menu-btn.secondary {
	background: rgba(255, 255, 255, 0.1);
	color: #ffffff;
	border: 2rpx solid rgba(255, 255, 255, 0.2);
}

.btn-icon {
	font-size: 40rpx;
}

.footer {
	text-align: center;
	margin-top: 40rpx;
}

.version {
	color: #ffffff;
	opacity: 0.6;
	font-size: 24rpx;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: #ffffff;
	border-radius: 20rpx;
	width: 90%;
	max-height: 80%;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx;
	border-bottom: 1rpx solid #eee;
}

.modal-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f5f5f5;
	border: none;
	font-size: 40rpx;
	color: #666;
}

.modal-body {
	padding: 40rpx;
	max-height: 600rpx;
}

.rule-text {
	font-size: 32rpx;
	line-height: 1.6;
	color: #333;
}
</style>
