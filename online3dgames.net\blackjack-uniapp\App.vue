<template>
	<view id="app">
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>
		<view class="app-content">
			<router-view />
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusBarHeight: 0
		}
	},
	onLaunch: function() {
		console.log('App Launch')
		this.initApp()
	},
	onShow: function() {
		console.log('App Show')
	},
	onHide: function() {
		console.log('App Hide')
	},
	methods: {
		initApp() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight || 0
			
			this.initAudio()
			this.loadGameSettings()
		},
		initAudio() {
			this.audioContext = uni.createInnerAudioContext()
			this.audioContext.autoplay = false
		},
		loadGameSettings() {
			try {
				const settings = uni.getStorageSync('blackjack_settings')
				if (settings) {
					this.globalData.gameSettings = JSON.parse(settings)
				}
			} catch (e) {
				console.log('Failed to load settings:', e)
			}
		}
	},
	globalData: {
		gameSettings: {
			soundEnabled: true,
			effectsVolume: 0.5,
			deckCount: 6,
			playerCount: 1,
			dealerStandsOnSoft17: true,
			surrenderAllowed: true,
			insuranceAllowed: true,
			doubleAfterSplitAllowed: true,
			resplitAcesAllowed: false,
			blackjackPayout: 1.5
		},
		gameState: {
			balance: 1000,
			totalGames: 0,
			totalWins: 0,
			highestWin: 0,
			currentWinStreak: 0,
			bestWinStreak: 0
		}
	}
}
</script>

<style>
@import url("./static/css/common.css");

#app {
	width: 100%;
	height: 100vh;
	background: #1a4b3a;
}

.status-bar {
	background: #1a4b3a;
}

.app-content {
	flex: 1;
	height: calc(100vh - var(--status-bar-height));
}

page {
	background: #1a4b3a;
	height: 100%;
}
</style>
