<template>
	<view class="game-container">
		<!-- 顶部状态栏 -->
		<view class="top-status">
			<view class="balance-info">
				<text class="balance-label">Balance:</text>
				<text class="balance-value">${{ game.balance }}</text>
			</view>
			<view class="bet-info">
				<text class="bet-label">Bet:</text>
				<text class="bet-value">${{ game.currentBet }}</text>
			</view>
			<view class="deck-info">
				<text class="deck-label">Cards:</text>
				<text class="deck-value">{{ game.deck.length }}</text>
			</view>
		</view>

		<!-- 庄家区域 -->
		<view class="dealer-section">
			<view class="dealer-label">Dealer: {{ game.dealerScore }}</view>
			<view class="dealer-cards">
				<view 
					v-for="(card, index) in game.dealerCards" 
					:key="'dealer-' + index"
					class="card"
					:class="[card.color, { 'card-back': isCardH<PERSON><PERSON>(card, index) }]"
				>
					<view v-if="!isCardHidden(card, index)" class="card-content">
						<text class="card-value">{{ card.value }}</text>
						<text class="card-suit">{{ card.suit }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 玩家区域 -->
		<view class="player-section">
			<view class="player-label">Player: {{ game.playerScore }}</view>
			<view class="player-cards">
				<view 
					v-for="(card, index) in game.playerCards" 
					:key="'player-' + index"
					class="card"
					:class="card.color"
				>
					<view class="card-content">
						<text class="card-value">{{ card.value }}</text>
						<text class="card-suit">{{ card.suit }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 游戏状态 -->
		<view v-if="gameMessage" class="game-status">
			<text class="status-text">{{ gameMessage }}</text>
		</view>

		<!-- 筹码区域 -->
		<view v-if="game.gamePhase === 'waiting'" class="betting-section">
			<view class="chips-container">
				<button 
					v-for="chip in chipValues" 
					:key="chip"
					class="chip"
					:class="'chip-' + chip"
					:disabled="game.balance < chip"
					@click="placeBet(chip)"
				>
					{{ chip }}
				</button>
			</view>
			<view class="betting-controls">
				<button class="game-btn" @click="clearBet" :disabled="game.currentBet === 0">
					Clear Bet
				</button>
				<button class="game-btn primary" @click="startGame" :disabled="game.currentBet === 0">
					Deal Cards
				</button>
			</view>
		</view>

		<!-- 游戏操作按钮 -->
		<view v-if="game.gamePhase === 'playing'" class="action-section">
			<view class="action-buttons">
				<button class="game-btn" @click="hit">Hit</button>
				<button class="game-btn" @click="stand">Stand</button>
				<button 
					class="game-btn warning" 
					@click="doubleDown" 
					:disabled="!game.canDoubleDown"
				>
					Double
				</button>
				<button 
					class="game-btn warning" 
					@click="split" 
					:disabled="!game.canSplit"
				>
					Split
				</button>
				<button 
					class="game-btn danger" 
					@click="surrender" 
					:disabled="!game.canSurrender"
				>
					Surrender
				</button>
			</view>
		</view>

		<!-- 结果模态框 -->
		<view v-if="showResultModal" class="modal-overlay" @click="hideResultModal">
			<view class="modal-content result-modal" @click.stop>
				<view class="result-header">
					<text class="result-title">{{ gameResult.result.toUpperCase() }}</text>
					<text class="result-icon">{{ getResultIcon(gameResult.result) }}</text>
				</view>
				<view class="result-body">
					<text class="result-reason">{{ gameResult.reason }}</text>
					<text class="result-payout">Payout: ${{ gameResult.payout }}</text>
				</view>
				<view class="result-actions">
					<button class="game-btn primary" @click="newGame">New Game</button>
					<button class="game-btn" @click="goHome">Home</button>
				</view>
			</view>
		</view>

		<!-- 游戏结束模态框 -->
		<view v-if="showGameOverModal" class="modal-overlay">
			<view class="modal-content game-over-modal">
				<view class="game-over-header">
					<text class="game-over-title">Game Over</text>
					<text class="game-over-icon">💸</text>
				</view>
				<view class="game-over-body">
					<text class="game-over-message">Your balance has run out!</text>
					<text class="game-over-tip">Don't worry, every great player has faced setbacks.</text>
				</view>
				<view class="game-over-actions">
					<button class="game-btn primary" @click="restartGame">Restart Game</button>
					<button class="game-btn" @click="goHome">Home</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { BlackjackGame } from '../../utils/gameLogic.js'

export default {
	data() {
		return {
			game: null,
			gameMessage: '',
			gameResult: null,
			showResultModal: false,
			showGameOverModal: false,
			chipValues: [1, 5, 10, 25, 50, 100, 500],
			audioContext: null
		}
	},
	onLoad() {
		this.initGame()
		this.initAudio()
	},
	onUnload() {
		this.saveGameState()
	},
	methods: {
		initGame() {
			this.game = new BlackjackGame()
			this.game.createDeck(6)
			this.loadGameState()
			this.updateGameMessage()
		},
		initAudio() {
			this.audioContext = uni.createInnerAudioContext()
			this.audioContext.autoplay = false
		},
		loadGameState() {
			try {
				const savedState = uni.getStorageSync('blackjack_game_state')
				if (savedState) {
					const state = JSON.parse(savedState)
					this.game.balance = state.balance || 1000
				}
			} catch (e) {
				console.log('Failed to load game state:', e)
			}
		},
		saveGameState() {
			try {
				const state = {
					balance: this.game.balance
				}
				uni.setStorageSync('blackjack_game_state', JSON.stringify(state))
			} catch (e) {
				console.log('Failed to save game state:', e)
			}
		},
		updateGameMessage() {
			switch (this.game.gamePhase) {
				case 'waiting':
					this.gameMessage = 'Place your bet to start playing'
					break
				case 'dealing':
					this.gameMessage = 'Dealing cards...'
					break
				case 'playing':
					this.gameMessage = 'Choose your action: Hit or Stand?'
					break
				case 'dealer':
					this.gameMessage = 'Dealer is playing...'
					break
				case 'finished':
					this.gameMessage = ''
					break
				default:
					this.gameMessage = ''
			}
		},
		isCardHidden(card, index) {
			return this.game.gamePhase !== 'finished' && 
				   this.game.gamePhase !== 'dealer' && 
				   index === 1 && 
				   this.game.dealerHiddenCard
		},
		placeBet(amount) {
			if (this.game.placeBet(amount)) {
				this.playSound('chip')
				this.updateGameMessage()
			} else {
				uni.showToast({
					title: 'Insufficient balance!',
					icon: 'none'
				})
			}
		},
		clearBet() {
			this.game.clearBet()
			this.updateGameMessage()
		},
		startGame() {
			if (this.game.currentBet === 0) {
				uni.showToast({
					title: 'Please place a bet first!',
					icon: 'none'
				})
				return
			}
			
			this.game.startNewGame()
			this.playSound('deal')
			this.updateGameMessage()
			
			// 检查是否有21点
			if (this.game.playerScore === 21) {
				setTimeout(() => {
					this.stand()
				}, 1000)
			}
		},
		hit() {
			const result = this.game.hit()
			this.playSound('deal')
			this.updateGameMessage()
			
			if (result === 'bust') {
				setTimeout(() => {
					this.showGameResult()
				}, 500)
			} else if (result === 'twenty-one') {
				setTimeout(() => {
					this.stand()
				}, 500)
			}
		},
		stand() {
			this.game.stand()
			this.updateGameMessage()
			
			setTimeout(() => {
				this.showGameResult()
			}, 1500)
		},
		doubleDown() {
			if (this.game.doubleDown()) {
				this.playSound('deal')
				this.updateGameMessage()
				
				setTimeout(() => {
					this.showGameResult()
				}, 1500)
			}
		},
		split() {
			if (this.game.split()) {
				this.playSound('deal')
				this.updateGameMessage()
			}
		},
		surrender() {
			if (this.game.surrender()) {
				this.showGameResult()
			}
		},
		showGameResult() {
			this.gameResult = this.game.endGame()
			this.showResultModal = true
			
			if (this.gameResult.result === 'win' || this.gameResult.result === 'blackjack') {
				this.playSound('win')
			}
			
			this.saveGameState()
			
			// 检查是否破产
			if (this.game.balance === 0) {
				setTimeout(() => {
					this.showResultModal = false
					this.showGameOverModal = true
				}, 2000)
			}
		},
		hideResultModal() {
			this.showResultModal = false
		},
		newGame() {
			this.showResultModal = false
			this.game.gamePhase = 'waiting'
			this.game.currentBet = 0
			this.updateGameMessage()
		},
		restartGame() {
			this.showGameOverModal = false
			this.game.balance = 1000
			this.newGame()
		},
		goHome() {
			uni.navigateBack()
		},
		getResultIcon(result) {
			switch (result) {
				case 'win':
				case 'blackjack':
					return '🎉'
				case 'lose':
					return '😞'
				case 'push':
					return '🤝'
				default:
					return '🎲'
			}
		},
		playSound(type) {
			// 音效播放逻辑
			try {
				let soundFile = ''
				switch (type) {
					case 'deal':
						soundFile = '/static/audio/deal.mp3'
						break
					case 'chip':
						soundFile = '/static/audio/deal.mp3' // 使用deal音效作为替代
						break
					case 'win':
						soundFile = '/static/audio/win.mp3'
						break
				}
				
				if (soundFile && this.audioContext) {
					this.audioContext.src = soundFile
					this.audioContext.play()
				}
			} catch (e) {
				console.log('Audio play failed:', e)
			}
		}
	}
}
</script>

<style scoped>
.game-container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background: linear-gradient(135deg, #1a4b3a 0%, #2d5a4a 100%);
	padding: 20rpx;
}

.top-status {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 16rpx;
	padding: 20rpx;
	margin-bottom: 30rpx;
}

.balance-info, .bet-info, .deck-info {
	text-align: center;
}

.balance-label, .bet-label, .deck-label {
	display: block;
	font-size: 24rpx;
	color: #ffffff;
	opacity: 0.8;
	margin-bottom: 8rpx;
}

.balance-value, .bet-value, .deck-value {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #ffd700;
}

.dealer-section, .player-section {
	margin-bottom: 40rpx;
}

.dealer-label, .player-label {
	font-size: 32rpx;
	font-weight: bold;
	color: #ffffff;
	text-align: center;
	margin-bottom: 20rpx;
}

.dealer-cards, .player-cards {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	gap: 8rpx;
	min-height: 180rpx;
}

.game-status {
	text-align: center;
	margin: 20rpx 0;
}

.status-text {
	font-size: 28rpx;
	color: #ffd700;
	font-weight: bold;
}

.betting-section {
	margin-top: auto;
}

.chips-container {
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	gap: 16rpx;
	margin-bottom: 30rpx;
}

.betting-controls {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}

.action-section {
	margin-top: auto;
}

.action-buttons {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
	justify-content: center;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: #ffffff;
	border-radius: 20rpx;
	width: 90%;
	max-width: 600rpx;
	overflow: hidden;
}

.result-modal .result-header {
	background: linear-gradient(135deg, #ffd700, #ffed4e);
	padding: 40rpx;
	text-align: center;
}

.result-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #1a4b3a;
	margin-bottom: 16rpx;
}

.result-icon {
	font-size: 60rpx;
}

.result-body {
	padding: 40rpx;
	text-align: center;
}

.result-reason {
	display: block;
	font-size: 32rpx;
	color: #333;
	margin-bottom: 20rpx;
}

.result-payout {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #16a34a;
}

.result-actions {
	display: flex;
	gap: 20rpx;
	padding: 40rpx;
	justify-content: center;
}

.game-over-modal .game-over-header {
	background: linear-gradient(135deg, #dc2626, #b91c1c);
	padding: 40rpx;
	text-align: center;
}

.game-over-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #ffffff;
	margin-bottom: 16rpx;
}

.game-over-icon {
	font-size: 60rpx;
}

.game-over-body {
	padding: 40rpx;
	text-align: center;
}

.game-over-message {
	display: block;
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.game-over-tip {
	display: block;
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

.game-over-actions {
	display: flex;
	gap: 20rpx;
	padding: 40rpx;
	justify-content: center;
}
</style>
