<template>
	<view class="game-container">
		<view class="game-header">
			<text class="balance">Balance: ${{ balance }}</text>
			<text class="bet">Bet: ${{ currentBet }}</text>
		</view>
		
		<view class="game-area">
			<view class="dealer-area">
				<text class="area-title">Dealer</text>
				<view class="cards">
					<view class="card" v-for="(card, index) in dealerCards" :key="index">
						<text class="card-text">{{ card.display }}</text>
					</view>
				</view>
				<text class="score">{{ dealerScore }}</text>
			</view>
			
			<view class="player-area">
				<text class="area-title">Player</text>
				<view class="cards">
					<view class="card" v-for="(card, index) in playerCards" :key="index">
						<text class="card-text">{{ card.display }}</text>
					</view>
				</view>
				<text class="score">{{ playerScore }}</text>
			</view>
		</view>
		
		<view class="game-controls">
			<button class="game-btn" @click="hit" :disabled="gameOver">Hit</button>
			<button class="game-btn" @click="stand" :disabled="gameOver">Stand</button>
			<button class="game-btn" @click="newGame">New Game</button>
		</view>
		
		<view class="game-message" v-if="message">
			<text>{{ message }}</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			balance: 1000,
			currentBet: 10,
			playerCards: [],
			dealerCards: [],
			playerScore: 0,
			dealerScore: 0,
			gameOver: false,
			message: '',
			deck: []
		}
	},
	onLoad() {
		this.newGame()
	},
	methods: {
		createDeck() {
			const suits = ['♠', '♥', '♦', '♣']
			const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
			this.deck = []
			
			for (let suit of suits) {
				for (let rank of ranks) {
					this.deck.push({
						suit: suit,
						rank: rank,
						value: this.getCardValue(rank),
						display: rank + suit
					})
				}
			}
			
			// Shuffle deck
			for (let i = this.deck.length - 1; i > 0; i--) {
				const j = Math.floor(Math.random() * (i + 1))
				;[this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]]
			}
		},
		
		getCardValue(rank) {
			if (rank === 'A') return 11
			if (['J', 'Q', 'K'].includes(rank)) return 10
			return parseInt(rank)
		},
		
		calculateScore(cards) {
			let score = 0
			let aces = 0
			
			for (let card of cards) {
				score += card.value
				if (card.rank === 'A') aces++
			}
			
			while (score > 21 && aces > 0) {
				score -= 10
				aces--
			}
			
			return score
		},
		
		dealCard(to) {
			if (this.deck.length === 0) this.createDeck()
			const card = this.deck.pop()
			
			if (to === 'player') {
				this.playerCards.push(card)
				this.playerScore = this.calculateScore(this.playerCards)
			} else {
				this.dealerCards.push(card)
				this.dealerScore = this.calculateScore(this.dealerCards)
			}
		},
		
		newGame() {
			this.createDeck()
			this.playerCards = []
			this.dealerCards = []
			this.gameOver = false
			this.message = ''
			
			// Deal initial cards
			this.dealCard('player')
			this.dealCard('dealer')
			this.dealCard('player')
			
			this.checkForBlackjack()
		},
		
		hit() {
			if (this.gameOver) return
			
			this.dealCard('player')
			
			if (this.playerScore > 21) {
				this.message = 'Bust! You lose!'
				this.gameOver = true
				this.balance -= this.currentBet
			}
		},
		
		stand() {
			if (this.gameOver) return
			
			// Dealer draws until 17 or higher
			while (this.dealerScore < 17) {
				this.dealCard('dealer')
			}
			
			this.determineWinner()
		},
		
		checkForBlackjack() {
			if (this.playerScore === 21) {
				this.message = 'Blackjack! You win!'
				this.gameOver = true
				this.balance += Math.floor(this.currentBet * 1.5)
			}
		},
		
		determineWinner() {
			if (this.dealerScore > 21) {
				this.message = 'Dealer busts! You win!'
				this.balance += this.currentBet
			} else if (this.dealerScore > this.playerScore) {
				this.message = 'Dealer wins!'
				this.balance -= this.currentBet
			} else if (this.playerScore > this.dealerScore) {
				this.message = 'You win!'
				this.balance += this.currentBet
			} else {
				this.message = 'Push! It\'s a tie!'
			}
			
			this.gameOver = true
		}
	}
}
</script>

<style scoped>
.game-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #1a4b3a, #2d5a4a);
	padding: 40rpx;
}

.game-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 40rpx;
	color: #ffd700;
	font-size: 32rpx;
	font-weight: bold;
}

.game-area {
	margin-bottom: 60rpx;
}

.dealer-area, .player-area {
	margin-bottom: 40rpx;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 30rpx;
}

.area-title {
	font-size: 36rpx;
	color: #ffffff;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
}

.cards {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.card {
	width: 80rpx;
	height: 120rpx;
	background: #ffffff;
	border-radius: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: bold;
	color: #000000;
}

.score {
	font-size: 32rpx;
	color: #ffd700;
	font-weight: bold;
}

.game-controls {
	display: flex;
	gap: 20rpx;
	margin-bottom: 40rpx;
}

.game-btn {
	flex: 1;
	height: 80rpx;
	background: linear-gradient(135deg, #ffd700, #ffed4e);
	color: #1a4b3a;
	border: none;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.game-btn:disabled {
	opacity: 0.5;
}

.game-message {
	text-align: center;
	font-size: 36rpx;
	color: #ffd700;
	font-weight: bold;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 30rpx;
}
</style>
