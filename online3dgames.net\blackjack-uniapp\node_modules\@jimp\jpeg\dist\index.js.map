{"version": 3, "sources": ["../src/index.js"], "names": ["MIME_TYPE", "mime", "constants", "MIME_JPEG", "decoders", "JPEG", "decode", "encoders", "image", "encode", "bitmap", "_quality", "data", "quality", "n", "cb", "throwError", "call", "Math", "round"], "mappings": ";;;;;;;;;;;AAAA;;AACA;;AAEA,IAAMA,SAAS,GAAG,YAAlB;;eAEe;AAAA,SAAO;AACpBC,IAAAA,IAAI,uCAAKD,SAAL,EAAiB,CAAC,MAAD,EAAS,KAAT,EAAgB,KAAhB,CAAjB,CADgB;AAGpBE,IAAAA,SAAS,EAAE;AACTC,MAAAA,SAAS,EAAEH;AADF,KAHS;AAOpBI,IAAAA,QAAQ,uCACLJ,SADK,EACOK,mBAAKC,MADZ,CAPY;AAWpBC,IAAAA,QAAQ,uCACLP,SADK,EACO,UAAAQ,KAAK;AAAA,aAAIH,mBAAKI,MAAL,CAAYD,KAAK,CAACE,MAAlB,EAA0BF,KAAK,CAACG,QAAhC,EAA0CC,IAA9C;AAAA,KADZ,CAXY;AAepB,aAAO;AACL;AACAD,MAAAA,QAAQ,EAAE,GAFL;;AAGL;;;;;;AAMAE,MAAAA,OATK,mBASGC,CATH,EASMC,EATN,EASU;AACb,YAAI,OAAOD,CAAP,KAAa,QAAjB,EAA2B;AACzB,iBAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,oBAAtB,EAA4CF,EAA5C,CAAP;AACD;;AAED,YAAID,CAAC,GAAG,CAAJ,IAASA,CAAC,GAAG,GAAjB,EAAsB;AACpB,iBAAOE,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,4BAAtB,EAAoDF,EAApD,CAAP;AACD;;AAED,aAAKJ,QAAL,GAAgBO,IAAI,CAACC,KAAL,CAAWL,CAAX,CAAhB;;AAEA,YAAI,0BAAcC,EAAd,CAAJ,EAAuB;AACrBA,UAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,eAAO,IAAP;AACD;AAzBI;AAfa,GAAP;AAAA,C", "sourcesContent": ["import JPEG from 'jpeg-js';\nimport { throwError, isNodePattern } from '@jimp/utils';\n\nconst MIME_TYPE = 'image/jpeg';\n\nexport default () => ({\n  mime: { [MIME_TYPE]: ['jpeg', 'jpg', 'jpe'] },\n\n  constants: {\n    MIME_JPEG: MIME_TYPE\n  },\n\n  decoders: {\n    [MIME_TYPE]: JPEG.decode\n  },\n\n  encoders: {\n    [MIME_TYPE]: image => JPEG.encode(image.bitmap, image._quality).data\n  },\n\n  class: {\n    // The quality to be used when saving JPEG images\n    _quality: 100,\n    /**\n     * Sets the quality of the image when saving as JPEG format (default is 100)\n     * @param {number} n The quality to use 0-100\n     * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n     * @returns {Jimp} this for chaining of methods\n     */\n    quality(n, cb) {\n      if (typeof n !== 'number') {\n        return throwError.call(this, 'n must be a number', cb);\n      }\n\n      if (n < 0 || n > 100) {\n        return throwError.call(this, 'n must be a number 0 - 100', cb);\n      }\n\n      this._quality = Math.round(n);\n\n      if (isNodePattern(cb)) {\n        cb.call(this, null, this);\n      }\n\n      return this;\n    }\n  }\n});\n"], "file": "index.js"}