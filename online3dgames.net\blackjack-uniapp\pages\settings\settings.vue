<template>
	<view class="container">
		<view class="header">
			<text class="title">Settings</text>
		</view>
		
		<view class="settings-list">
			<!-- 音效设置 -->
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Sound Effects</text>
					<text class="setting-desc">Enable game sound effects</text>
				</view>
				<switch 
					:checked="settings.soundEnabled" 
					@change="onSoundChange"
					color="#ffd700"
				/>
			</view>
			
			<!-- 音量设置 -->
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Volume</text>
					<text class="setting-desc">Adjust sound volume</text>
				</view>
				<slider 
					:value="settings.volume * 100"
					@change="onVolumeChange"
					activeColor="#ffd700"
					backgroundColor="rgba(255,255,255,0.2)"
					block-color="#ffd700"
					:disabled="!settings.soundEnabled"
				/>
			</view>
			
			<!-- 牌组数量 -->
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Number of Decks</text>
					<text class="setting-desc">{{ settings.deckCount }} deck(s)</text>
				</view>
				<picker 
					:range="deckOptions" 
					:value="deckIndex"
					@change="onDeckChange"
				>
					<view class="picker-text">{{ settings.deckCount }}</view>
				</picker>
			</view>
			
			<!-- 庄家软17规则 -->
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Dealer Hits Soft 17</text>
					<text class="setting-desc">Dealer must hit on soft 17</text>
				</view>
				<switch 
					:checked="!settings.dealerStandsOnSoft17" 
					@change="onSoft17Change"
					color="#ffd700"
				/>
			</view>
			
			<!-- 投降规则 -->
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Surrender Allowed</text>
					<text class="setting-desc">Allow early surrender</text>
				</view>
				<switch 
					:checked="settings.surrenderAllowed" 
					@change="onSurrenderChange"
					color="#ffd700"
				/>
			</view>
			
			<!-- 保险规则 -->
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Insurance Allowed</text>
					<text class="setting-desc">Allow insurance bets</text>
				</view>
				<switch 
					:checked="settings.insuranceAllowed" 
					@change="onInsuranceChange"
					color="#ffd700"
				/>
			</view>
			
			<!-- 21点赔率 -->
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Blackjack Payout</text>
					<text class="setting-desc">{{ settings.blackjackPayout }}:1 payout</text>
				</view>
				<picker 
					:range="payoutOptions" 
					:value="payoutIndex"
					@change="onPayoutChange"
				>
					<view class="picker-text">{{ settings.blackjackPayout }}:1</view>
				</picker>
			</view>
		</view>
		
		<view class="action-buttons">
			<button class="game-btn danger" @click="resetSettings">Reset to Default</button>
			<button class="game-btn" @click="resetGameData">Reset Game Data</button>
		</view>
		
		<view class="footer">
			<text class="app-info">Blackjack 21 v1.0.0</text>
			<text class="copyright">© 2024 Flow Fray Games</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			settings: {
				soundEnabled: true,
				volume: 0.5,
				deckCount: 6,
				dealerStandsOnSoft17: true,
				surrenderAllowed: true,
				insuranceAllowed: true,
				blackjackPayout: 1.5
			},
			deckOptions: [1, 2, 4, 6, 8],
			payoutOptions: ['1.5', '1.2', '1.0']
		}
	},
	computed: {
		deckIndex() {
			return this.deckOptions.indexOf(this.settings.deckCount)
		},
		payoutIndex() {
			return this.payoutOptions.indexOf(this.settings.blackjackPayout.toString())
		}
	},
	onLoad() {
		this.loadSettings()
	},
	methods: {
		loadSettings() {
			try {
				const savedSettings = uni.getStorageSync('blackjack_settings')
				if (savedSettings) {
					this.settings = { ...this.settings, ...JSON.parse(savedSettings) }
				}
			} catch (e) {
				console.log('Failed to load settings:', e)
			}
		},
		saveSettings() {
			try {
				uni.setStorageSync('blackjack_settings', JSON.stringify(this.settings))
				uni.showToast({
					title: 'Settings saved',
					icon: 'success'
				})
			} catch (e) {
				console.log('Failed to save settings:', e)
				uni.showToast({
					title: 'Save failed',
					icon: 'error'
				})
			}
		},
		onSoundChange(e) {
			this.settings.soundEnabled = e.detail.value
			this.saveSettings()
		},
		onVolumeChange(e) {
			this.settings.volume = e.detail.value / 100
			this.saveSettings()
		},
		onDeckChange(e) {
			this.settings.deckCount = this.deckOptions[e.detail.value]
			this.saveSettings()
		},
		onSoft17Change(e) {
			this.settings.dealerStandsOnSoft17 = !e.detail.value
			this.saveSettings()
		},
		onSurrenderChange(e) {
			this.settings.surrenderAllowed = e.detail.value
			this.saveSettings()
		},
		onInsuranceChange(e) {
			this.settings.insuranceAllowed = e.detail.value
			this.saveSettings()
		},
		onPayoutChange(e) {
			this.settings.blackjackPayout = parseFloat(this.payoutOptions[e.detail.value])
			this.saveSettings()
		},
		resetSettings() {
			uni.showModal({
				title: 'Reset Settings',
				content: 'Are you sure you want to reset all settings to default?',
				success: (res) => {
					if (res.confirm) {
						this.settings = {
							soundEnabled: true,
							volume: 0.5,
							deckCount: 6,
							dealerStandsOnSoft17: true,
							surrenderAllowed: true,
							insuranceAllowed: true,
							blackjackPayout: 1.5
						}
						this.saveSettings()
					}
				}
			})
		},
		resetGameData() {
			uni.showModal({
				title: 'Reset Game Data',
				content: 'This will reset your balance and statistics. Are you sure?',
				success: (res) => {
					if (res.confirm) {
						try {
							uni.removeStorageSync('blackjack_game_state')
							uni.removeStorageSync('blackjack_stats')
							uni.showToast({
								title: 'Game data reset',
								icon: 'success'
							})
						} catch (e) {
							uni.showToast({
								title: 'Reset failed',
								icon: 'error'
							})
						}
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	background: linear-gradient(135deg, #1a4b3a 0%, #2d5a4a 100%);
	padding: 40rpx;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 60rpx;
	font-weight: bold;
	color: #ffd700;
}

.settings-list {
	flex: 1;
}

.setting-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.setting-info {
	flex: 1;
}

.setting-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.setting-desc {
	display: block;
	font-size: 24rpx;
	color: #ffffff;
	opacity: 0.7;
}

.picker-text {
	color: #ffd700;
	font-weight: bold;
	font-size: 32rpx;
	text-align: right;
	min-width: 80rpx;
}

.action-buttons {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin: 40rpx 0;
}

.footer {
	text-align: center;
	margin-top: 40rpx;
}

.app-info {
	display: block;
	font-size: 28rpx;
	color: #ffffff;
	opacity: 0.8;
	margin-bottom: 10rpx;
}

.copyright {
	display: block;
	font-size: 24rpx;
	color: #ffffff;
	opacity: 0.6;
}
</style>
