<template>
	<view class="container">
		<view class="header">
			<text class="title">Settings</text>
		</view>
		
		<view class="settings-list">
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Sound Effects</text>
					<text class="setting-desc">Enable game sound effects</text>
				</view>
				<switch 
					:checked="settings.soundEnabled" 
					@change="onSoundChange"
					color="#ffd700"
				/>
			</view>
			
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Volume</text>
					<text class="setting-desc">Adjust sound volume</text>
				</view>
				<slider 
					:value="settings.volume * 100"
					@change="onVolumeChange"
					activeColor="#ffd700"
					backgroundColor="rgba(255,255,255,0.2)"
					block-color="#ffd700"
					:disabled="!settings.soundEnabled"
				/>
			</view>
			
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Deck Count</text>
					<text class="setting-desc">Number of decks in play</text>
				</view>
				<picker 
					:range="deckOptions" 
					:value="deckIndex"
					@change="onDeckChange"
				>
					<view class="picker-text">{{ settings.deckCount }} Deck{{ settings.deckCount > 1 ? 's' : '' }}</view>
				</picker>
			</view>
			
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Dealer Stands on Soft 17</text>
					<text class="setting-desc">Dealer behavior on soft 17</text>
				</view>
				<switch 
					:checked="settings.dealerStandsOnSoft17" 
					@change="onSoft17Change"
					color="#ffd700"
				/>
			</view>
			
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Surrender Allowed</text>
					<text class="setting-desc">Allow player to surrender</text>
				</view>
				<switch 
					:checked="settings.surrenderAllowed" 
					@change="onSurrenderChange"
					color="#ffd700"
				/>
			</view>
			
			<view class="setting-item">
				<view class="setting-info">
					<text class="setting-title">Insurance Allowed</text>
					<text class="setting-desc">Allow insurance bets</text>
				</view>
				<switch 
					:checked="settings.insuranceAllowed" 
					@change="onInsuranceChange"
					color="#ffd700"
				/>
			</view>
		</view>
		
		<view class="action-buttons">
			<button class="action-btn danger" @click="resetSettings">
				Reset Settings
			</button>
			<button class="action-btn danger" @click="resetGameData">
				Reset Game Data
			</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			settings: {
				soundEnabled: true,
				volume: 0.5,
				deckCount: 6,
				dealerStandsOnSoft17: true,
				surrenderAllowed: true,
				insuranceAllowed: true,
				blackjackPayout: 1.5
			},
			deckOptions: [1, 2, 4, 6, 8]
		}
	},
	computed: {
		deckIndex() {
			return this.deckOptions.indexOf(this.settings.deckCount)
		}
	},
	onLoad() {
		this.loadSettings()
	},
	methods: {
		loadSettings() {
			try {
				const savedSettings = uni.getStorageSync('blackjack_settings')
				if (savedSettings) {
					this.settings = { ...this.settings, ...JSON.parse(savedSettings) }
				}
			} catch (e) {
				console.log('Failed to load settings:', e)
			}
		},
		saveSettings() {
			try {
				uni.setStorageSync('blackjack_settings', JSON.stringify(this.settings))
				uni.showToast({
					title: 'Settings saved',
					icon: 'success'
				})
			} catch (e) {
				console.log('Failed to save settings:', e)
				uni.showToast({
					title: 'Save failed',
					icon: 'error'
				})
			}
		},
		onSoundChange(e) {
			this.settings.soundEnabled = e.detail.value
			this.saveSettings()
		},
		onVolumeChange(e) {
			this.settings.volume = e.detail.value / 100
			this.saveSettings()
		},
		onDeckChange(e) {
			this.settings.deckCount = this.deckOptions[e.detail.value]
			this.saveSettings()
		},
		onSoft17Change(e) {
			this.settings.dealerStandsOnSoft17 = e.detail.value
			this.saveSettings()
		},
		onSurrenderChange(e) {
			this.settings.surrenderAllowed = e.detail.value
			this.saveSettings()
		},
		onInsuranceChange(e) {
			this.settings.insuranceAllowed = e.detail.value
			this.saveSettings()
		},
		resetSettings() {
			uni.showModal({
				title: 'Reset Settings',
				content: 'Are you sure you want to reset all settings to default?',
				success: (res) => {
					if (res.confirm) {
						this.settings = {
							soundEnabled: true,
							volume: 0.5,
							deckCount: 6,
							dealerStandsOnSoft17: true,
							surrenderAllowed: true,
							insuranceAllowed: true,
							blackjackPayout: 1.5
						}
						this.saveSettings()
					}
				}
			})
		},
		resetGameData() {
			uni.showModal({
				title: 'Reset Game Data',
				content: 'This will reset your balance and statistics. Are you sure?',
				success: (res) => {
					if (res.confirm) {
						try {
							uni.removeStorageSync('blackjack_game_state')
							uni.removeStorageSync('blackjack_stats')
							uni.showToast({
								title: 'Game data reset',
								icon: 'success'
							})
						} catch (e) {
							uni.showToast({
								title: 'Reset failed',
								icon: 'error'
							})
						}
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #1a4b3a, #2d5a4a);
	padding: 40rpx;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
}

.title {
	font-size: 48rpx;
	font-weight: bold;
	color: #ffd700;
}

.settings-list {
	margin-bottom: 60rpx;
}

.setting-item {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.setting-info {
	flex: 1;
}

.setting-title {
	font-size: 32rpx;
	color: #ffffff;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.setting-desc {
	font-size: 26rpx;
	color: #ffffff;
	opacity: 0.7;
	display: block;
}

.picker-text {
	color: #ffd700;
	font-size: 28rpx;
	font-weight: bold;
}

.action-buttons {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.action-btn {
	height: 80rpx;
	border-radius: 40rpx;
	border: none;
	font-size: 32rpx;
	font-weight: bold;
}

.action-btn.danger {
	background: linear-gradient(135deg, #dc2626, #b91c1c);
	color: #ffffff;
}
</style>
