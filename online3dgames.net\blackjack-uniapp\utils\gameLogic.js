// 游戏核心逻辑
export class BlackjackGame {
	constructor() {
		this.deck = []
		this.discardPile = []
		this.dealerCards = []
		this.playerCards = []
		this.dealerScore = 0
		this.playerScore = 0
		this.gamePhase = 'waiting' // waiting, betting, dealing, playing, dealer, finished
		this.balance = 1000
		this.currentBet = 0
		this.gameInProgress = false
		this.dealerHiddenCard = null
		this.insuranceOffered = false
		this.canSplit = false
		this.canDoubleDown = false
		this.canSurrender = false
		this.splitHands = []
		this.currentHandIndex = 0
	}

	// 创建牌组
	createDeck(deckCount = 6) {
		const suits = ['♠', '♥', '♦', '♣']
		const values = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']
		
		this.deck = []
		
		for (let d = 0; d < deckCount; d++) {
			for (let suit of suits) {
				for (let value of values) {
					this.deck.push({
						suit: suit,
						value: value,
						numericValue: this.getCardNumericValue(value),
						color: (suit === '♥' || suit === '♦') ? 'red' : 'black'
					})
				}
			}
		}
		
		this.shuffleDeck()
	}

	// 洗牌
	shuffleDeck() {
		for (let i = this.deck.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1))
			;[this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]]
		}
	}

	// 获取卡牌数值
	getCardNumericValue(value) {
		if (value === 'A') return 11
		if (['J', 'Q', 'K'].includes(value)) return 10
		return parseInt(value)
	}

	// 发牌
	dealCard() {
		if (this.deck.length === 0) {
			this.reshuffleDeck()
		}
		return this.deck.pop()
	}

	// 重新洗牌
	reshuffleDeck() {
		this.deck = [...this.discardPile]
		this.discardPile = []
		this.shuffleDeck()
	}

	// 计算手牌分数
	calculateScore(cards) {
		let score = 0
		let aces = 0
		
		for (let card of cards) {
			if (card.value === 'A') {
				aces++
				score += 11
			} else {
				score += card.numericValue
			}
		}
		
		// 处理A的软硬转换
		while (score > 21 && aces > 0) {
			score -= 10
			aces--
		}
		
		return score
	}

	// 开始新游戏
	startNewGame() {
		if (this.deck.length < 20) {
			this.createDeck(6)
		}
		
		this.dealerCards = []
		this.playerCards = []
		this.dealerScore = 0
		this.playerScore = 0
		this.gamePhase = 'dealing'
		this.gameInProgress = true
		this.dealerHiddenCard = null
		this.insuranceOffered = false
		this.canSplit = false
		this.canDoubleDown = false
		this.canSurrender = false
		this.splitHands = []
		this.currentHandIndex = 0
		
		// 发初始牌
		this.dealInitialCards()
	}

	// 发初始牌
	dealInitialCards() {
		// 玩家第一张牌
		this.playerCards.push(this.dealCard())
		// 庄家第一张牌（明牌）
		this.dealerCards.push(this.dealCard())
		// 玩家第二张牌
		this.playerCards.push(this.dealCard())
		// 庄家第二张牌（暗牌）
		const hiddenCard = this.dealCard()
		this.dealerCards.push(hiddenCard)
		this.dealerHiddenCard = hiddenCard
		
		this.playerScore = this.calculateScore(this.playerCards)
		this.dealerScore = this.calculateScore([this.dealerCards[0]]) // 只计算明牌
		
		this.gamePhase = 'playing'
		this.updateGameOptions()
	}

	// 更新游戏选项
	updateGameOptions() {
		this.canDoubleDown = this.playerCards.length === 2 && this.balance >= this.currentBet
		this.canSplit = this.playerCards.length === 2 && 
						this.playerCards[0].value === this.playerCards[1].value &&
						this.balance >= this.currentBet
		this.canSurrender = this.playerCards.length === 2
		
		// 检查保险
		if (this.dealerCards[0].value === 'A' && !this.insuranceOffered) {
			this.insuranceOffered = true
		}
	}

	// 玩家要牌
	hit() {
		if (this.gamePhase !== 'playing') return false
		
		this.playerCards.push(this.dealCard())
		this.playerScore = this.calculateScore(this.playerCards)
		
		this.canDoubleDown = false
		this.canSplit = false
		this.canSurrender = false
		
		if (this.playerScore > 21) {
			this.gamePhase = 'finished'
			this.endGame()
			return 'bust'
		} else if (this.playerScore === 21) {
			this.stand()
			return 'twenty-one'
		}
		
		return 'continue'
	}

	// 玩家停牌
	stand() {
		if (this.gamePhase !== 'playing') return
		
		this.gamePhase = 'dealer'
		this.dealerTurn()
	}

	// 加倍
	doubleDown() {
		if (!this.canDoubleDown) return false
		
		this.balance -= this.currentBet
		this.currentBet *= 2
		
		const result = this.hit()
		if (result === 'continue') {
			this.stand()
		}
		
		return true
	}

	// 分牌
	split() {
		if (!this.canSplit) return false
		
		this.balance -= this.currentBet
		
		// 创建分牌手
		this.splitHands = [
			{ cards: [this.playerCards[0]], bet: this.currentBet },
			{ cards: [this.playerCards[1]], bet: this.currentBet }
		]
		
		this.currentHandIndex = 0
		this.playerCards = this.splitHands[0].cards
		this.playerCards.push(this.dealCard())
		this.playerScore = this.calculateScore(this.playerCards)
		
		this.updateGameOptions()
		return true
	}

	// 投降
	surrender() {
		if (!this.canSurrender) return false
		
		this.balance += Math.floor(this.currentBet / 2)
		this.currentBet = 0
		this.gamePhase = 'finished'
		this.endGame()
		
		return true
	}

	// 庄家回合
	dealerTurn() {
		// 翻开暗牌
		this.dealerScore = this.calculateScore(this.dealerCards)
		
		// 庄家必须在16点或以下要牌，17点或以上停牌
		while (this.dealerScore < 17) {
			this.dealerCards.push(this.dealCard())
			this.dealerScore = this.calculateScore(this.dealerCards)
		}
		
		this.gamePhase = 'finished'
		this.endGame()
	}

	// 结束游戏
	endGame() {
		const result = this.determineWinner()
		this.gameInProgress = false
		return result
	}

	// 判断胜负
	determineWinner() {
		const playerBust = this.playerScore > 21
		const dealerBust = this.dealerScore > 21
		const playerBlackjack = this.playerScore === 21 && this.playerCards.length === 2
		const dealerBlackjack = this.dealerScore === 21 && this.dealerCards.length === 2
		
		if (playerBust) {
			return { result: 'lose', reason: 'Player bust', payout: 0 }
		}
		
		if (dealerBust) {
			const payout = playerBlackjack ? this.currentBet * 2.5 : this.currentBet * 2
			this.balance += payout
			return { result: 'win', reason: 'Dealer bust', payout: payout }
		}
		
		if (playerBlackjack && !dealerBlackjack) {
			const payout = this.currentBet * 2.5
			this.balance += payout
			return { result: 'blackjack', reason: 'Player blackjack', payout: payout }
		}
		
		if (dealerBlackjack && !playerBlackjack) {
			return { result: 'lose', reason: 'Dealer blackjack', payout: 0 }
		}
		
		if (this.playerScore > this.dealerScore) {
			const payout = this.currentBet * 2
			this.balance += payout
			return { result: 'win', reason: 'Higher score', payout: payout }
		} else if (this.playerScore < this.dealerScore) {
			return { result: 'lose', reason: 'Lower score', payout: 0 }
		} else {
			this.balance += this.currentBet
			return { result: 'push', reason: 'Same score', payout: this.currentBet }
		}
	}

	// 下注
	placeBet(amount) {
		if (this.balance >= amount && amount > 0) {
			this.balance -= amount
			this.currentBet += amount
			return true
		}
		return false
	}

	// 清除下注
	clearBet() {
		this.balance += this.currentBet
		this.currentBet = 0
	}

	// 获取游戏状态
	getGameState() {
		return {
			deck: this.deck,
			dealerCards: this.dealerCards,
			playerCards: this.playerCards,
			dealerScore: this.dealerScore,
			playerScore: this.playerScore,
			gamePhase: this.gamePhase,
			balance: this.balance,
			currentBet: this.currentBet,
			gameInProgress: this.gameInProgress,
			canSplit: this.canSplit,
			canDoubleDown: this.canDoubleDown,
			canSurrender: this.canSurrender,
			insuranceOffered: this.insuranceOffered
		}
	}
}
