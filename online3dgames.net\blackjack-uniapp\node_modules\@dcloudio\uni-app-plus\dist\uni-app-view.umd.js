!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t={exports:{}},n={exports:{}},r={exports:{}},i=r.exports={version:"2.6.12"};"number"==typeof __e&&(__e=i);var a=r.exports,o={exports:{}},s=o.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=s);var l=o.exports,u=a,c=l,d="__core-js_shared__",h=c[d]||(c[d]={});(n.exports=function(e,t){return h[e]||(h[e]=void 0!==t?t:{})})("versions",[]).push({version:u.version,mode:"global",copyright:"© 2020 <PERSON> (zloirock.ru)"});var f=n.exports,p=0,v=Math.random(),g=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++p+v).toString(36))},m=f("wks"),_=g,y=l.Symbol,b="function"==typeof y;(t.exports=function(e){return m[e]||(m[e]=b&&y[e]||(b?y:_)("Symbol."+e))}).store=m;var w,x,S=t.exports,k={},T=function(e){return"object"==typeof e?null!==e:"function"==typeof e},E=T,C=function(e){if(!E(e))throw TypeError(e+" is not an object!");return e},O=function(e){try{return!!e()}catch(t){return!0}},M=!O((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}));function L(){if(x)return w;x=1;var e=T,t=l.document,n=e(t)&&e(t.createElement);return w=function(e){return n?t.createElement(e):{}}}var I=!M&&!O((function(){return 7!=Object.defineProperty(L()("div"),"a",{get:function(){return 7}}).a})),A=T,B=C,N=I,R=function(e,t){if(!A(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!A(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!A(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!A(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")},P=Object.defineProperty;k.f=M?Object.defineProperty:function(e,t,n){if(B(e),t=R(t,!0),B(n),N)try{return P(e,t,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e};var D=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},z=k,F=D,$=M?function(e,t,n){return z.f(e,t,F(1,n))}:function(e,t,n){return e[t]=n,e},j=S("unscopables"),V=Array.prototype;null==V[j]&&$(V,j,{});var W={},H={}.toString,U=function(e){return H.call(e).slice(8,-1)},q=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e},Y=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==U(e)?e.split(""):Object(e)},X=q,Z=function(e){return Y(X(e))},G={exports:{}},K={}.hasOwnProperty,J=function(e,t){return K.call(e,t)},Q=f("native-function-to-string",Function.toString),ee=l,te=$,ne=J,re=g("src"),ie=Q,ae="toString",oe=(""+ie).split(ae);a.inspectSource=function(e){return ie.call(e)},(G.exports=function(e,t,n,r){var i="function"==typeof n;i&&(ne(n,"name")||te(n,"name",t)),e[t]!==n&&(i&&(ne(n,re)||te(n,re,e[t]?""+e[t]:oe.join(String(t)))),e===ee?e[t]=n:r?e[t]?e[t]=n:te(e,t,n):(delete e[t],te(e,t,n)))})(Function.prototype,ae,(function(){return"function"==typeof this&&this[re]||ie.call(this)}));var se=G.exports,le=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e},ue=le,ce=l,de=a,he=$,fe=se,pe=function(e,t,n){if(ue(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}},ve="prototype",ge=function(e,t,n){var r,i,a,o,s=e&ge.F,l=e&ge.G,u=e&ge.S,c=e&ge.P,d=e&ge.B,h=l?ce:u?ce[t]||(ce[t]={}):(ce[t]||{})[ve],f=l?de:de[t]||(de[t]={}),p=f[ve]||(f[ve]={});for(r in l&&(n=t),n)a=((i=!s&&h&&void 0!==h[r])?h:n)[r],o=d&&i?pe(a,ce):c&&"function"==typeof a?pe(Function.call,a):a,h&&fe(h,r,a,e&ge.U),f[r]!=a&&he(f,r,o),c&&p[r]!=a&&(p[r]=a)};ce.core=de,ge.F=1,ge.G=2,ge.S=4,ge.P=8,ge.B=16,ge.W=32,ge.U=64,ge.R=128;var me,_e,ye,be=ge,we=Math.ceil,xe=Math.floor,Se=function(e){return isNaN(e=+e)?0:(e>0?xe:we)(e)},ke=Se,Te=Math.min,Ee=Se,Ce=Math.max,Oe=Math.min,Me=Z,Le=function(e){return e>0?Te(ke(e),9007199254740991):0},Ie=function(e,t){return(e=Ee(e))<0?Ce(e+t,0):Oe(e,t)},Ae=f("keys"),Be=g,Ne=function(e){return Ae[e]||(Ae[e]=Be(e))},Re=J,Pe=Z,De=(me=!1,function(e,t,n){var r,i=Me(e),a=Le(i.length),o=Ie(n,a);if(me&&t!=t){for(;a>o;)if((r=i[o++])!=r)return!0}else for(;a>o;o++)if((me||o in i)&&i[o]===t)return me||o||0;return!me&&-1}),ze=Ne("IE_PROTO"),Fe="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(","),$e=function(e,t){var n,r=Pe(e),i=0,a=[];for(n in r)n!=ze&&Re(r,n)&&a.push(n);for(;t.length>i;)Re(r,n=t[i++])&&(~De(a,n)||a.push(n));return a},je=Fe,Ve=Object.keys||function(e){return $e(e,je)},We=k,He=C,Ue=Ve,qe=M?Object.defineProperties:function(e,t){He(e);for(var n,r=Ue(t),i=r.length,a=0;i>a;)We.f(e,n=r[a++],t[n]);return e};var Ye=C,Xe=qe,Ze=Fe,Ge=Ne("IE_PROTO"),Ke=function(){},Je="prototype",Qe=function(){var e,t=L()("iframe"),n=Ze.length;for(t.style.display="none",function(){if(ye)return _e;ye=1;var e=l.document;return _e=e&&e.documentElement}().appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),Qe=e.F;n--;)delete Qe[Je][Ze[n]];return Qe()},et=Object.create||function(e,t){var n;return null!==e?(Ke[Je]=Ye(e),n=new Ke,Ke[Je]=null,n[Ge]=e):n=Qe(),void 0===t?n:Xe(n,t)},tt=k.f,nt=J,rt=S("toStringTag"),it=function(e,t,n){e&&!nt(e=n?e:e.prototype,rt)&&tt(e,rt,{configurable:!0,value:t})},at=et,ot=D,st=it,lt={};$(lt,S("iterator"),(function(){return this}));var ut=q,ct=function(e){return Object(ut(e))},dt=J,ht=ct,ft=Ne("IE_PROTO"),pt=Object.prototype,vt=Object.getPrototypeOf||function(e){return e=ht(e),dt(e,ft)?e[ft]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?pt:null},gt=be,mt=se,_t=$,yt=W,bt=function(e,t,n){e.prototype=at(lt,{next:ot(1,n)}),st(e,t+" Iterator")},wt=it,xt=vt,St=S("iterator"),kt=!([].keys&&"next"in[].keys()),Tt="keys",Et="values",Ct=function(){return this},Ot=function(e){V[j][e]=!0},Mt=function(e,t){return{value:t,done:!!e}},Lt=W,It=Z,At=function(e,t,n,r,i,a,o){bt(n,t,r);var s,l,u,c=function(e){if(!kt&&e in p)return p[e];switch(e){case Tt:case Et:return function(){return new n(this,e)}}return function(){return new n(this,e)}},d=t+" Iterator",h=i==Et,f=!1,p=e.prototype,v=p[St]||p["@@iterator"]||i&&p[i],g=v||c(i),m=i?h?c("entries"):g:void 0,_="Array"==t&&p.entries||v;if(_&&(u=xt(_.call(new e)))!==Object.prototype&&u.next&&(wt(u,d,!0),"function"!=typeof u[St]&&_t(u,St,Ct)),h&&v&&v.name!==Et&&(f=!0,g=function(){return v.call(this)}),(kt||f||!p[St])&&_t(p,St,g),yt[t]=g,yt[d]=Ct,i)if(s={values:h?g:c(Et),keys:a?g:c(Tt),entries:m},o)for(l in s)l in p||mt(p,l,s[l]);else gt(gt.P+gt.F*(kt||f),t,s);return s}(Array,"Array",(function(e,t){this._t=It(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,Mt(1)):Mt(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])}),"values");Lt.Arguments=Lt.Array,Ot("keys"),Ot("values"),Ot("entries");for(var Bt=At,Nt=Ve,Rt=se,Pt=l,Dt=$,zt=W,Ft=S,$t=Ft("iterator"),jt=Ft("toStringTag"),Vt=zt.Array,Wt={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},Ht=Nt(Wt),Ut=0;Ut<Ht.length;Ut++){var qt,Yt=Ht[Ut],Xt=Wt[Yt],Zt=Pt[Yt],Gt=Zt&&Zt.prototype;if(Gt&&(Gt[$t]||Dt(Gt,$t,Vt),Gt[jt]||Dt(Gt,jt,Yt),zt[Yt]=Vt,Xt))for(qt in Bt)Gt[qt]||Rt(Gt,qt,Bt[qt],!0)}
/**
  * @vue/shared v3.4.21
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **/function Kt(e,t){var n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}var Jt,Qt={},en=[],tn=()=>{},nn=()=>!1,rn=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),an=e=>e.startsWith("onUpdate:"),on=Object.assign,sn=(e,t)=>{var n=e.indexOf(t);n>-1&&e.splice(n,1)},ln=Object.prototype.hasOwnProperty,un=(e,t)=>ln.call(e,t),cn=Array.isArray,dn=e=>"[object Map]"===yn(e),hn=e=>"[object Set]"===yn(e),fn=e=>"function"==typeof e,pn=e=>"string"==typeof e,vn=e=>"symbol"==typeof e,gn=e=>null!==e&&"object"==typeof e,mn=e=>(gn(e)||fn(e))&&fn(e.then)&&fn(e.catch),_n=Object.prototype.toString,yn=e=>_n.call(e),bn=e=>yn(e).slice(8,-1),wn=e=>"[object Object]"===yn(e),xn=e=>pn(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,Sn=Kt(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),kn=e=>{var t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Tn=/-(\w)/g,En=kn((e=>e.replace(Tn,((e,t)=>t?t.toUpperCase():"")))),Cn=/\B([A-Z])/g,On=kn((e=>e.replace(Cn,"-$1").toLowerCase())),Mn=kn((e=>e.charAt(0).toUpperCase()+e.slice(1))),Ln=kn((e=>e?"on".concat(Mn(e)):"")),In=(e,t)=>!Object.is(e,t),An=(e,t)=>{for(var n=0;n<e.length;n++)e[n](t)},Bn=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},Nn=e=>{var t=parseFloat(e);return isNaN(t)?e:t},Rn=()=>Jt||(Jt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window||"undefined"!=typeof window?window:{});function Pn(e){if(cn(e)){for(var t={},n=0;n<e.length;n++){var r=e[n],i=pn(r)?$n(r):Pn(r);if(i)for(var a in i)t[a]=i[a]}return t}if(pn(e)||gn(e))return e}var Dn=/;(?![^(]*\))/g,zn=/:([^]+)/,Fn=/\/\*[^]*?\*\//g;function $n(e){var t={};return e.replace(Fn,"").split(Dn).forEach((e=>{if(e){var n=e.split(zn);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function jn(e){var t="";if(pn(e))t=e;else if(cn(e))for(var n=0;n<e.length;n++){var r=jn(e[n]);r&&(t+=r+" ")}else if(gn(e))for(var i in e)e[i]&&(t+=i+" ");return t.trim()}var Vn,Wn,Hn=Kt("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Un(e){return!!e||""===e}var qn=be,Yn=le,Xn=ct,Zn=O,Gn=[].sort,Kn=[1,2,3];qn(qn.P+qn.F*(Zn((function(){Kn.sort(void 0)}))||!Zn((function(){Kn.sort(null)}))||!function(){if(Wn)return Vn;Wn=1;var e=O;return Vn=function(t,n){return!!t&&e((function(){n?t.call(null,(function(){}),1):t.call(null)}))}}()(Gn)),"Array",{sort:function(e){return void 0===e?Gn.call(Xn(this)):Gn.call(Xn(this),Yn(e))}});var Jn="\n",Qn=44,er="#007aff",tr=/^([a-z-]+:)?\/\//i,nr=/^data:.*,.*/,rr="wxs://",ir="json://",ar="wxsModules",or="renderjsModules",sr="onThemeChange",lr="onPageScroll",ur="onReachBottom",cr=0;function dr(e){var t=Date.now(),n=cr?t-cr:0;cr=t;for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];return"[".concat(t,"][").concat(n,"ms][").concat(e,"]：").concat(i.map((e=>JSON.stringify(e))).join(" "))}function hr(e){return function(e){return 0===e.indexOf("/")}(e)?e:"/"+e}function fr(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return function(){if(e){for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];t=e.apply(n,i),e=null}return t}}function pr(e,t){if(pn(t)){var n=(t=t.replace(/\[(\d+)\]/g,".$1")).split("."),r=n[0];return e||(e={}),1===n.length?e[r]:pr(e[r],n.slice(1).join("."))}}function vr(e){return En(e.substring(5))}var gr=fr((()=>{var e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&((this.__uniDataset||(this.__uniDataset={}))[vr(e)]=n);t.call(this,e,n)};var n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[vr(e)],n.call(this,e)}}));function mr(e){return on({},e.dataset,e.__uniDataset)}var _r=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function yr(e){return{passive:e}}function br(e){var{id:t,offsetTop:n,offsetLeft:r}=e;return{id:t,dataset:mr(e),offsetTop:n,offsetLeft:r}}function wr(e){if(fn(e))return window.plus?e():void document.addEventListener("plusready",e)}var xr=/(?:Once|Passive|Capture)$/;function Sr(e){var t,n;if(xr.test(e))for(t={};n=e.match(xr);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0;return[On(e.slice(2)),t]}var kr=(()=>({stop:1,prevent:2,self:4}))(),Tr="class",Er="style",Cr=".vShow",Or=".vOwnerId",Mr=".vRenderjs",Lr="change:",Ir=1,Ar=2,Br=3,Nr=4,Rr=5,Pr=6,Dr=7,zr=8,Fr=9,$r=10,jr=12,Vr=15,Wr=20;var Hr=function(){};Hr.prototype={on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var r=this;function i(){r.off(e,i),t.apply(n,arguments)}return i._=t,this.on(e,i,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,i=n.length;r<i;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],i=[];if(r&&t){for(var a=r.length-1;a>=0;a--)if(r[a].fn===t||r[a].fn._===t){r.splice(a,1);break}i=r}return i.length?n[e]=i:delete n[e],this}};var Ur=Hr,qr=["{","}"];var Yr=/^(?:\d)+/,Xr=/^(?:\w)+/;var Zr="zh-Hans",Gr="zh-Hant",Kr="en",Jr="fr",Qr="es",ei=Object.prototype.hasOwnProperty,ti=(e,t)=>ei.call(e,t),ni=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:qr;if(!t)return[e];var r=this._caches[e];return r||(r=function(e,t){var[n,r]=t,i=[],a=0,o="";for(;a<e.length;){var s=e[a++];if(s===n){o&&i.push({type:"text",value:o}),o="";var l="";for(s=e[a++];void 0!==s&&s!==r;)l+=s,s=e[a++];var u=s===r,c=Yr.test(l)?"list":u&&Xr.test(l)?"named":"unknown";i.push({value:l,type:c})}else o+=s}return o&&i.push({type:"text",value:o}),i}(e,n),this._caches[e]=r),function(e,t){var n=[],r=0,i=Array.isArray(t)?"list":(a=t,null!==a&&"object"==typeof a?"named":"unknown");var a;if("unknown"===i)return n;for(;r<e.length;){var o=e[r];switch(o.type){case"text":n.push(o.value);break;case"list":n.push(t[parseInt(o.value,10)]);break;case"named":"named"===i&&n.push(t[o.value])}r++}return n}(r,t)}};function ri(e,t){if(e){if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return Zr;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?Zr:e.indexOf("-hant")>-1?Gr:(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?Gr:Zr);var n,r=[Kr,Jr,Qr];t&&Object.keys(t).length>0&&(r=Object.keys(t));var i=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,r);return i||void 0}}class ii{constructor(e){var{locale:t,fallbackLocale:n,messages:r,watcher:i,formater:a}=e;this.locale=Kr,this.fallbackLocale=Kr,this.message={},this.messages={},this.watchers=[],n&&(this.fallbackLocale=n),this.formater=a||ni,this.messages=r||{},this.setLocale(t||Kr),i&&this.watchLocale(i)}setLocale(e){var t=this.locale;this.locale=ri(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){var t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=this.messages[e];r?n?Object.assign(r,t):Object.keys(t).forEach((e=>{ti(r,e)||(r[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){var r=this.message;return"string"==typeof t?(t=ri(t,this.messages))&&(r=this.messages[t]):n=t,ti(r,e)?this.formater.interpolate(r[e],n).join(""):(console.warn("Cannot translate the value of keypath ".concat(e,". Use the value of keypath as default.")),e)}}function ai(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;if("string"!=typeof e){var i=[t,e];e=i[0],t=i[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&uni.getLocale?uni.getLocale():"undefined"!=typeof window&&window.getLocale?window.getLocale():Kr),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||Kr);var a=new ii({locale:e,fallbackLocale:n,messages:t,watcher:r}),o=(e,t)=>{if("function"!=typeof getApp)o=function(e,t){return a.t(e,t)};else{var n=!1;o=function(e,t){var r=getApp().$vm;return r&&(r.$locale,n||(n=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(r,a))),a.t(e,t)}}return o(e,t)};return{i18n:a,f:(e,t,n)=>a.f(e,t,n),t:(e,t)=>o(e,t),add(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return a.add(e,t,n)},watch:e=>a.watchLocale(e),getLocale:()=>a.getLocale(),setLocale:e=>a.setLocale(e)}}var oi,si=fr((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));function li(){var e;if(!oi&&(e="function"==typeof getApp?weex.requireModule("plus").getLanguage():plus.webview.currentWebview().getStyle().locale,oi=ai(e),si())){var t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>oi.add(e,__uniConfig.locales[e]))),oi.setLocale(e)}return oi}function ui(e,t,n){return t.reduce(((t,r,i)=>(t[e+r]=n[i],t)),{})}var ci=fr((()=>{var e="uni.picker.",t=["done","cancel"];li().add(Kr,ui(e,t,["Done","Cancel"]),!1),li().add(Qr,ui(e,t,["OK","Cancelar"]),!1),li().add(Jr,ui(e,t,["OK","Annuler"]),!1),li().add(Zr,ui(e,t,["完成","取消"]),!1),li().add(Gr,ui(e,t,["完成","取消"]),!1)})),di=fr((()=>{var e="uni.button.",t=["feedback.title","feedback.send"];li().add(Kr,ui(e,t,["feedback","send"]),!1),li().add(Qr,ui(e,t,["realimentación","enviar"]),!1),li().add(Jr,ui(e,t,["retour d'information","envoyer"]),!1),li().add(Zr,ui(e,t,["问题反馈","发送"]),!1),li().add(Gr,ui(e,t,["問題反饋","發送"]),!1)}));function hi(e){var t=new Ur;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t.emit(e,...r)},subscribe(n,r){t[arguments.length>2&&void 0!==arguments[2]&&arguments[2]?"once":"on"]("".concat(e,".").concat(n),r)},unsubscribe(n,r){t.off("".concat(e,".").concat(n),r)},subscribeHandler(n,r,i){t.emit("".concat(e,".").concat(n),r,i)}}}var fi="invokeViewApi",pi="invokeServiceApi",vi=1,gi=Object.create(null);function mi(e,t){return e+"."+t}function _i(e,t,n){t=mi(e,t),gi[t]||(gi[t]=n)}function yi(e,t){var{id:n,name:r,args:i}=e;r=mi(t,r);var a=e=>{n&&UniViewJSBridge.publishHandler(fi+"."+n,e)},o=gi[r];o?o(i,a):a({})}var bi,wi=on(hi("service"),{invokeServiceMethod:(e,t,n)=>{var{subscribe:r,publishHandler:i}=UniViewJSBridge,a=n?vi++:0;n&&r(pi+"."+a,n,!0),i(pi,{id:a,name:e,args:t})}}),xi=350,Si=10,ki=yr(!0);function Ti(){bi&&(clearTimeout(bi),bi=null)}var Ei,Ci,Oi=0,Mi=0;function Li(e){if(Ti(),1===e.touches.length){var{pageX:t,pageY:n}=e.touches[0];Oi=t,Mi=n,bi=setTimeout((function(){var t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),xi)}}function Ii(e){if(bi){if(1!==e.touches.length)return Ti();var{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Oi)>Si||Math.abs(n-Mi)>Si?Ti():void 0}}function Ai(e,t){var n=Number(e);return isNaN(n)?t:n}function Bi(){var e=__uniConfig.globalStyle||{},t=Ai(e.rpxCalcMaxDeviceWidth,960),n=Ai(e.rpxCalcBaseDeviceWidth,375);function r(){var e,r,i,a=(e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,r=e&&90===Math.abs(window.orientation),i=e?Math[r?"max":"min"](screen.width,screen.height):screen.width,Math.min(window.innerWidth,document.documentElement.clientWidth,i)||i);a=a<=t?a:n,document.documentElement.style.fontSize=a/23.4375+"px"}r(),document.addEventListener("DOMContentLoaded",r),window.addEventListener("load",r),window.addEventListener("resize",r)}function Ni(){Bi(),gr(),window.addEventListener("touchstart",Li,ki),window.addEventListener("touchmove",Ii,ki),window.addEventListener("touchend",Ti,ki),window.addEventListener("touchcancel",Ti,ki)}class Ri{constructor(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Ei,!e&&Ei&&(this.index=(Ei.scopes||(Ei.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){var t=Ei;try{return Ei=this,e()}finally{Ei=t}}}on(){Ei=this}off(){Ei=this.parent}stop(e){if(this._active){var t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){var r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this._active=!1}}}class Pi{constructor(e,t,n,r){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ei;t&&t.active&&t.effects.push(e)}(this,r)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Wi();for(var e=0;e<this._depsLength;e++){var t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),Hi()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();var e=$i,t=Ci;try{return $i=!0,Ci=this,this._runnings++,Di(this),this.fn()}finally{zi(this),this._runnings--,Ci=t,$i=e}}stop(){var e;this.active&&(Di(this),zi(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function Di(e){e._trackId++,e._depsLength=0}function zi(e){if(e.deps.length>e._depsLength){for(var t=e._depsLength;t<e.deps.length;t++)Fi(e.deps[t],e);e.deps.length=e._depsLength}}function Fi(e,t){var n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}var $i=!0,ji=0,Vi=[];function Wi(){Vi.push($i),$i=!1}function Hi(){var e=Vi.pop();$i=void 0===e||e}function Ui(){ji++}function qi(){for(ji--;!ji&&Xi.length;)Xi.shift()()}function Yi(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);var r=e.deps[e._depsLength];r!==t?(r&&Fi(r,e),e.deps[e._depsLength++]=t):e._depsLength++}}var Xi=[];function Zi(e,t,n){for(var r of(Ui(),e.keys())){var i=void 0;r._dirtyLevel<t&&(null!=i?i:i=e.get(r)===r._trackId)&&(r._shouldSchedule||(r._shouldSchedule=0===r._dirtyLevel),r._dirtyLevel=t),r._shouldSchedule&&(null!=i?i:i=e.get(r)===r._trackId)&&(r.trigger(),r._runnings&&!r.allowRecurse||2===r._dirtyLevel||(r._shouldSchedule=!1,r.scheduler&&Xi.push(r.scheduler)))}qi()}var Gi=(e,t)=>{var n=new Map;return n.cleanup=e,n.computed=t,n},Ki=new WeakMap,Ji=Symbol(""),Qi=Symbol("");function ea(e,t,n){if($i&&Ci){var r=Ki.get(e);r||Ki.set(e,r=new Map);var i=r.get(n);i||r.set(n,i=Gi((()=>r.delete(n)))),Yi(Ci,i)}}function ta(e,t,n,r,i,a){var o=Ki.get(e);if(o){var s=[];if("clear"===t)s=[...o.values()];else if("length"===n&&cn(e)){var l=Number(r);o.forEach(((e,t)=>{("length"===t||!vn(t)&&t>=l)&&s.push(e)}))}else switch(void 0!==n&&s.push(o.get(n)),t){case"add":cn(e)?xn(n)&&s.push(o.get("length")):(s.push(o.get(Ji)),dn(e)&&s.push(o.get(Qi)));break;case"delete":cn(e)||(s.push(o.get(Ji)),dn(e)&&s.push(o.get(Qi)));break;case"set":dn(e)&&s.push(o.get(Ji))}for(var u of(Ui(),s))u&&Zi(u,4);qi()}}var na=Kt("__proto__,__v_isRef,__isVue"),ra=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(vn)),ia=aa();function aa(){var e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(){for(var e=Ua(this),n=0,r=this.length;n<r;n++)ea(e,0,n+"");for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];var s=e[t](...a);return-1===s||!1===s?e[t](...a.map(Ua)):s}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(){Wi(),Ui();for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=Ua(this)[t].apply(this,n);return qi(),Hi(),i}})),e}function oa(e){var t=Ua(this);return ea(t,0,e),t.hasOwnProperty(e)}class sa{constructor(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this._isReadonly=e,this._isShallow=t}get(e,t,n){var r=this._isReadonly,i=this._isShallow;if("__v_isReactive"===t)return!r;if("__v_isReadonly"===t)return r;if("__v_isShallow"===t)return i;if("__v_raw"===t)return n===(r?i?Da:Pa:i?Ra:Na).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;var a=cn(e);if(!r){if(a&&un(ia,t))return Reflect.get(ia,t,n);if("hasOwnProperty"===t)return oa}var o=Reflect.get(e,t,n);return(vn(t)?ra.has(t):na(t))?o:(r||ea(e,0,t),i?o:Ja(o)?a&&xn(t)?o:o.value:gn(o)?r?Fa(o):za(o):o)}}class la extends sa{constructor(){super(!1,arguments.length>0&&void 0!==arguments[0]&&arguments[0])}set(e,t,n,r){var i=e[t];if(!this._isShallow){var a=Va(i);if(Wa(n)||Va(n)||(i=Ua(i),n=Ua(n)),!cn(e)&&Ja(i)&&!Ja(n))return!a&&(i.value=n,!0)}var o=cn(e)&&xn(t)?Number(t)<e.length:un(e,t),s=Reflect.set(e,t,n,r);return e===Ua(r)&&(o?In(n,i)&&ta(e,"set",t,n):ta(e,"add",t,n)),s}deleteProperty(e,t){var n=un(e,t);e[t];var r=Reflect.deleteProperty(e,t);return r&&n&&ta(e,"delete",t,void 0),r}has(e,t){var n=Reflect.has(e,t);return vn(t)&&ra.has(t)||ea(e,0,t),n}ownKeys(e){return ea(e,0,cn(e)?"length":Ji),Reflect.ownKeys(e)}}class ua extends sa{constructor(){super(!0,arguments.length>0&&void 0!==arguments[0]&&arguments[0])}set(e,t){return!0}deleteProperty(e,t){return!0}}var ca=new la,da=new ua,ha=new la(!0),fa=e=>e,pa=e=>Reflect.getPrototypeOf(e);function va(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=Ua(e=e.__v_raw),a=Ua(t);n||(In(t,a)&&ea(i,0,t),ea(i,0,a));var{has:o}=pa(i),s=r?fa:n?Xa:Ya;return o.call(i,t)?s(e.get(t)):o.call(i,a)?s(e.get(a)):void(e!==i&&e.get(t))}function ga(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.__v_raw,r=Ua(n),i=Ua(e);return t||(In(e,i)&&ea(r,0,e),ea(r,0,i)),e===i?n.has(e):n.has(e)||n.has(i)}function ma(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e=e.__v_raw,!t&&ea(Ua(e),0,Ji),Reflect.get(e,"size",e)}function _a(e){e=Ua(e);var t=Ua(this);return pa(t).has.call(t,e)||(t.add(e),ta(t,"add",e,e)),this}function ya(e,t){t=Ua(t);var n=Ua(this),{has:r,get:i}=pa(n),a=r.call(n,e);a||(e=Ua(e),a=r.call(n,e));var o=i.call(n,e);return n.set(e,t),a?In(t,o)&&ta(n,"set",e,t):ta(n,"add",e,t),this}function ba(e){var t=Ua(this),{has:n,get:r}=pa(t),i=n.call(t,e);i||(e=Ua(e),i=n.call(t,e)),r&&r.call(t,e);var a=t.delete(e);return i&&ta(t,"delete",e,void 0),a}function wa(){var e=Ua(this),t=0!==e.size,n=e.clear();return t&&ta(e,"clear",void 0,void 0),n}function xa(e,t){return function(n,r){var i=this,a=i.__v_raw,o=Ua(a),s=t?fa:e?Xa:Ya;return!e&&ea(o,0,Ji),a.forEach(((e,t)=>n.call(r,s(e),s(t),i)))}}function Sa(e,t,n){return function(){var r=this.__v_raw,i=Ua(r),a=dn(i),o="entries"===e||e===Symbol.iterator&&a,s="keys"===e&&a,l=r[e](...arguments),u=n?fa:t?Xa:Ya;return!t&&ea(i,0,s?Qi:Ji),{next(){var{value:e,done:t}=l.next();return t?{value:e,done:t}:{value:o?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function ka(e){return function(){return"delete"!==e&&("clear"===e?void 0:this)}}function Ta(){var e={get(e){return va(this,e)},get size(){return ma(this)},has:ga,add:_a,set:ya,delete:ba,clear:wa,forEach:xa(!1,!1)},t={get(e){return va(this,e,!1,!0)},get size(){return ma(this)},has:ga,add:_a,set:ya,delete:ba,clear:wa,forEach:xa(!1,!0)},n={get(e){return va(this,e,!0)},get size(){return ma(this,!0)},has(e){return ga.call(this,e,!0)},add:ka("add"),set:ka("set"),delete:ka("delete"),clear:ka("clear"),forEach:xa(!0,!1)},r={get(e){return va(this,e,!0,!0)},get size(){return ma(this,!0)},has(e){return ga.call(this,e,!0)},add:ka("add"),set:ka("set"),delete:ka("delete"),clear:ka("clear"),forEach:xa(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((i=>{e[i]=Sa(i,!1,!1),n[i]=Sa(i,!0,!1),t[i]=Sa(i,!1,!0),r[i]=Sa(i,!0,!0)})),[e,n,t,r]}var[Ea,Ca,Oa,Ma]=Ta();function La(e,t){var n=t?e?Ma:Oa:e?Ca:Ea;return(t,r,i)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(un(n,r)&&r in t?n:t,r,i)}var Ia={get:La(!1,!1)},Aa={get:La(!1,!0)},Ba={get:La(!0,!1)},Na=new WeakMap,Ra=new WeakMap,Pa=new WeakMap,Da=new WeakMap;function za(e){return Va(e)?e:$a(e,!1,ca,Ia,Na)}function Fa(e){return $a(e,!0,da,Ba,Pa)}function $a(e,t,n,r,i){if(!gn(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;var a=i.get(e);if(a)return a;var o,s=(o=e).__v_skip||!Object.isExtensible(o)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(bn(o));if(0===s)return e;var l=new Proxy(e,2===s?r:n);return i.set(e,l),l}function ja(e){return Va(e)?ja(e.__v_raw):!(!e||!e.__v_isReactive)}function Va(e){return!(!e||!e.__v_isReadonly)}function Wa(e){return!(!e||!e.__v_isShallow)}function Ha(e){return ja(e)||Va(e)}function Ua(e){var t=e&&e.__v_raw;return t?Ua(t):e}function qa(e){return Object.isExtensible(e)&&Bn(e,"__v_skip",!0),e}var Ya=e=>gn(e)?za(e):e,Xa=e=>gn(e)?Fa(e):e;class Za{constructor(e,t,n,r){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Pi((()=>e(this._value)),(()=>Ka(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=n}get value(){var e=Ua(this);return e._cacheable&&!e.effect.dirty||!In(e._value,e._value=e.effect.run())||Ka(e,4),Ga(e),e.effect._dirtyLevel>=2&&Ka(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function Ga(e){var t;$i&&Ci&&(e=Ua(e),Yi(Ci,null!=(t=e.dep)?t:e.dep=Gi((()=>e.dep=void 0),e instanceof Za?e:void 0)))}function Ka(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4,n=(e=Ua(e)).dep;n&&Zi(n,t)}function Ja(e){return!(!e||!0!==e.__v_isRef)}function Qa(e){return to(e,!1)}function eo(e){return to(e,!0)}function to(e,t){return Ja(e)?e:new no(e,t)}class no{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Ua(e),this._value=t?e:Ya(e)}get value(){return Ga(this),this._value}set value(e){var t=this.__v_isShallow||Wa(e)||Va(e);e=t?e:Ua(e),In(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Ya(e),Ka(this,4,e))}}var ro={get:(e,t,n)=>{return Ja(r=Reflect.get(e,t,n))?r.value:r;var r},set:(e,t,n,r)=>{var i=e[t];return Ja(i)&&!Ja(n)?(i.value=n,!0):Reflect.set(e,t,n,r)}};function io(e){return ja(e)?e:new Proxy(e,ro)}function ao(e,t,n,r){try{return r?e(...r):e()}catch(i){so(i,t,n)}}function oo(e,t,n,r){if(fn(e)){var i=ao(e,t,n,r);return i&&mn(i)&&i.catch((e=>{so(e,t,n)})),i}for(var a=[],o=0;o<e.length;o++)a.push(oo(e[o],t,n,r));return a}function so(e,t,n){if(t&&t.vnode,t){for(var r=t.parent,i=t.proxy,a="https://vuejs.org/error-reference/#runtime-".concat(n);r;){var o=r.ec;if(o)for(var s=0;s<o.length;s++)if(!1===o[s](e,i,a))return;r=r.parent}var l=t.appContext.config.errorHandler;if(l)return void ao(l,null,10,[e,i,a])}!function(e,t,n){e instanceof Error?console.error(e.message+"\n"+e.stack):console.error(e)}(e)}var lo=!1,uo=!1,co=[],ho=0,fo=[],po=null,vo=0,go=Promise.resolve(),mo=null;function _o(e){var t=mo||go;return e?t.then(this?e.bind(this):e):t}function yo(e){co.length&&co.includes(e,lo&&e.allowRecurse?ho+1:ho)||(null==e.id?co.push(e):co.splice(function(e){for(var t=ho+1,n=co.length;t<n;){var r=t+n>>>1,i=co[r],a=So(i);a<e||a===e&&i.pre?t=r+1:n=r}return t}(e.id),0,e),bo())}function bo(){lo||uo||(uo=!0,mo=go.then(To))}function wo(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:lo?ho+1:0;n<co.length;n++){var r=co[n];if(r&&r.pre){if(e&&r.id!==e.uid)continue;co.splice(n,1),n--,r()}}}function xo(e){if(fo.length){var t=[...new Set(fo)].sort(((e,t)=>So(e)-So(t)));if(fo.length=0,po)return void po.push(...t);for(po=t,vo=0;vo<po.length;vo++)po[vo]();po=null,vo=0}}var So=e=>null==e.id?1/0:e.id,ko=(e,t)=>{var n=So(e)-So(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function To(e){uo=!1,lo=!0,co.sort(ko);try{for(ho=0;ho<co.length;ho++){var t=co[ho];t&&!1!==t.active&&ao(t,null,14)}}finally{ho=0,co.length=0,xo(),lo=!1,mo=null,(co.length||fo.length)&&To()}}function Eo(e,t){if(!e.isUnmounted){for(var n=e.vnode.props||Qt,r=arguments.length,i=new Array(r>2?r-2:0),a=2;a<r;a++)i[a-2]=arguments[a];var o,s=i,l=t.startsWith("update:"),u=l&&t.slice(7);if(u&&u in n){var c="".concat("modelValue"===u?"model":u,"Modifiers"),{number:d,trim:h}=n[c]||Qt;h&&(s=i.map((e=>pn(e)?e.trim():e))),d&&(s=i.map(Nn))}var f=n[o=Ln(t)]||n[o=Ln(En(t))];!f&&l&&(f=n[o=Ln(On(t))]),f&&oo(f,e,6,s);var p=n[o+"Once"];if(p){if(e.emitted){if(e.emitted[o])return}else e.emitted={};e.emitted[o]=!0,oo(p,e,6,s)}}}function Co(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t.emitsCache,i=r.get(e);if(void 0!==i)return i;var a=e.emits,o={},s=!1;if(!fn(e)){var l=e=>{var n=Co(e,t,!0);n&&(s=!0,on(o,n))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return a||s?(cn(a)?a.forEach((e=>o[e]=null)):on(o,a),gn(e)&&r.set(e,o),o):(gn(e)&&r.set(e,null),null)}function Oo(e,t){return!(!e||!rn(t))&&(t=t.slice(2).replace(/Once$/,""),un(e,t[0].toLowerCase()+t.slice(1))||un(e,On(t))||un(e,t))}var Mo=null,Lo=null;function Io(e){var t=Mo;return Mo=e,Lo=e&&e.type.__scopeId||null,t}function Ao(e){var t,n,{type:r,vnode:i,proxy:a,withProxy:o,props:s,propsOptions:[l],slots:u,attrs:c,emit:d,render:h,renderCache:f,data:p,setupState:v,ctx:g,inheritAttrs:m}=e,_=Io(e);try{if(4&i.shapeFlag){var y=o||a,b=y;t=_l(h.call(b,y,f,s,v,p,g)),n=c}else{var w=r;0,t=_l(w.length>1?w(s,{attrs:c,slots:u,emit:d}):w(s,null)),n=r.props?c:Bo(c)}}catch(T){so(T,e,1),t=vl(al)}var x=t;if(n&&!1!==m){var S=Object.keys(n),{shapeFlag:k}=x;S.length&&7&k&&(l&&S.some(an)&&(n=No(n,l)),x=gl(x,n))}return i.dirs&&((x=gl(x)).dirs=x.dirs?x.dirs.concat(i.dirs):i.dirs),i.transition&&(x.transition=i.transition),t=x,Io(_),t}var Bo=e=>{var t;for(var n in e)("class"===n||"style"===n||rn(n))&&((t||(t={}))[n]=e[n]);return t},No=(e,t)=>{var n={};for(var r in e)an(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function Ro(e,t,n){var r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(var i=0;i<r.length;i++){var a=r[i];if(t[a]!==e[a]&&!Oo(n,a))return!0}return!1}var Po=Symbol.for("v-ndc");var Do=Symbol.for("v-scx"),zo=()=>Ns(Do);var Fo={};function $o(e,t,n){return jo(e,t,n)}function jo(e,t){var{immediate:n,deep:r,flush:i,once:a,onTrack:o,onTrigger:s}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Qt;if(t&&a){var l=t;t=function(){l(...arguments),k()}}var u,c,d=Cl,h=e=>!0===r?e:Ho(e,!1===r?1:void 0),f=!1,p=!1;if(Ja(e)?(u=()=>e.value,f=Wa(e)):ja(e)?(u=()=>h(e),f=!0):cn(e)?(p=!0,f=e.some((e=>ja(e)||Wa(e))),u=()=>e.map((e=>Ja(e)?e.value:ja(e)?h(e):fn(e)?ao(e,d,2):void 0))):u=fn(e)?t?()=>ao(e,d,2):()=>(c&&c(),oo(e,d,3,[m])):tn,t&&r){var v=u;u=()=>Ho(v())}var g,m=e=>{c=x.onStop=()=>{ao(e,d,4),c=x.onStop=void 0}};if(Rl){if(m=tn,t?n&&oo(t,d,3,[u(),p?[]:void 0,m]):u(),"sync"!==i)return tn;var _=zo();g=_.__watcherHandles||(_.__watcherHandles=[])}var y,b=p?new Array(e.length).fill(Fo):Fo,w=()=>{if(x.active&&x.dirty)if(t){var e=x.run();(r||f||(p?e.some(((e,t)=>In(e,b[t]))):In(e,b)))&&(c&&c(),oo(t,d,3,[e,b===Fo?void 0:p&&b[0]===Fo?[]:b,m]),b=e)}else x.run()};w.allowRecurse=!!t,"sync"===i?y=w:"post"===i?y=()=>Ks(w,d&&d.suspense):(w.pre=!0,d&&(w.id=d.uid),y=()=>yo(w));var x=new Pi(u,tn,y),S=Ei,k=()=>{x.stop(),S&&sn(S.effects,x)};return t?n?w():b=x.run():"post"===i?Ks(x.run.bind(x),d&&d.suspense):x.run(),g&&g.push(k),k}function Vo(e,t,n){var r,i=this.proxy,a=pn(e)?e.includes(".")?Wo(i,e):()=>i[e]:e.bind(i,i);fn(t)?r=t:(r=t.handler,n=t);var o=Il(this),s=jo(a,r.bind(i),n);return o(),s}function Wo(e,t){var n=t.split(".");return()=>{for(var t=e,r=0;r<n.length&&t;r++)t=t[n[r]];return t}}function Ho(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3?arguments[3]:void 0;if(!gn(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((r=r||new Set).has(e))return e;if(r.add(e),Ja(e))Ho(e.value,t,n,r);else if(cn(e))for(var i=0;i<e.length;i++)Ho(e[i],t,n,r);else if(hn(e)||dn(e))e.forEach((e=>{Ho(e,t,n,r)}));else if(wn(e))for(var a in e)Ho(e[a],t,n,r);return e}function Uo(e,t){if(null===Mo)return e;for(var n=zl(Mo)||Mo.proxy,r=e.dirs||(e.dirs=[]),i=0;i<t.length;i++){var[a,o,s,l=Qt]=t[i];a&&(fn(a)&&(a={mounted:a,updated:a}),a.deep&&Ho(o),r.push({dir:a,instance:n,value:o,oldValue:void 0,arg:s,modifiers:l}))}return e}function qo(e,t,n,r){for(var i=e.dirs,a=t&&t.dirs,o=0;o<i.length;o++){var s=i[o];a&&(s.oldValue=a[o].value);var l=s.dir[r];l&&(Wi(),oo(l,n,8,[e.el,s,e,t]),Hi())}}
/*! #__NO_SIDE_EFFECTS__ */function Yo(e,t){return fn(e)?(()=>on({name:e.name},t,{setup:e}))():e}var Xo=e=>!!e.type.__asyncLoader,Zo=e=>e.type.__isKeepAlive;function Go(e,t){Jo(e,"a",t)}function Ko(e,t){Jo(e,"da",t)}function Jo(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Cl,r=e.__wdc||(e.__wdc=()=>{for(var t=n;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(es(t,r,n),n)for(var i=n.parent;i&&i.parent;)Zo(i.parent.vnode)&&Qo(r,t,n,i),i=i.parent}function Qo(e,t,n,r){var i=es(t,e,r,!0);ss((()=>{sn(r[t],i)}),n)}function es(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Cl,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(n){var i=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=function(){if(!n.isUnmounted){Wi();for(var r=Il(n),i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];var s=oo(t,n,e,a);return r(),Hi(),s}});return r?i.unshift(a):i.push(a),a}}var ts=e=>function(t){return(!Rl||"sp"===e)&&es(e,(function(){return t(...arguments)}),arguments.length>1&&void 0!==arguments[1]?arguments[1]:Cl)},ns=ts("bm"),rs=ts("m"),is=ts("bu"),as=ts("u"),os=ts("bum"),ss=ts("um"),ls=ts("sp"),us=ts("rtg"),cs=ts("rtc");function ds(e){es("ec",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:Cl)}var hs=e=>e?Bl(e)?zl(e)||e.proxy:hs(e.parent):null,fs=on(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>hs(e.parent),$root:e=>hs(e.root),$emit:e=>e.emit,$options:e=>ws(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,yo(e.update)}),$nextTick:e=>e.n||(e.n=_o.bind(e.proxy)),$watch:e=>Vo.bind(e)}),ps=(e,t)=>e!==Qt&&!e.__isScriptSetup&&un(e,t),vs={get(e,t){var n,{_:r}=e,{ctx:i,setupState:a,data:o,props:s,accessCache:l,type:u,appContext:c}=r;if("$"!==t[0]){var d=l[t];if(void 0!==d)switch(d){case 1:return a[t];case 2:return o[t];case 4:return i[t];case 3:return s[t]}else{if(ps(a,t))return l[t]=1,a[t];if(o!==Qt&&un(o,t))return l[t]=2,o[t];if((n=r.propsOptions[0])&&un(n,t))return l[t]=3,s[t];if(i!==Qt&&un(i,t))return l[t]=4,i[t];ms&&(l[t]=0)}}var h,f,p=fs[t];return p?("$attrs"===t&&ea(r,0,t),p(r)):(h=u.__cssModules)&&(h=h[t])?h:i!==Qt&&un(i,t)?(l[t]=4,i[t]):(f=c.config.globalProperties,un(f,t)?f[t]:void 0)},set(e,t,n){var{_:r}=e,{data:i,setupState:a,ctx:o}=r;return ps(a,t)?(a[t]=n,!0):i!==Qt&&un(i,t)?(i[t]=n,!0):!un(r.props,t)&&(("$"!==t[0]||!(t.slice(1)in r))&&(o[t]=n,!0))},has(e,t){var n,{_:{data:r,setupState:i,accessCache:a,ctx:o,appContext:s,propsOptions:l}}=e;return!!a[t]||r!==Qt&&un(r,t)||ps(i,t)||(n=l[0])&&un(n,t)||un(o,t)||un(fs,t)||un(s.config.globalProperties,t)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:un(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function gs(e){return cn(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}var ms=!0;function _s(e){var t=ws(e),n=e.proxy,r=e.ctx;ms=!1,t.beforeCreate&&ys(t.beforeCreate,e,"bc");var i,{data:a,computed:o,methods:s,watch:l,provide:u,inject:c,created:d,beforeMount:h,mounted:f,beforeUpdate:p,updated:v,activated:g,deactivated:m,beforeDestroy:_,beforeUnmount:y,destroyed:b,unmounted:w,render:x,renderTracked:S,renderTriggered:k,errorCaptured:T,serverPrefetch:E,expose:C,inheritAttrs:O,components:M,directives:L,filters:I}=t;if(c&&function(e,t){cn(e)&&(e=Ts(e));var n=function(n){var r=e[n],i=void 0;Ja(i=gn(r)?"default"in r?Ns(r.from||n,r.default,!0):Ns(r.from||n):Ns(r))?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[n]=i};for(var r in e)n(r)}(c,r),s)for(var A in s){var B=s[A];fn(B)&&(r[A]=B.bind(n))}if(a&&(i=a.call(n,n),gn(i)&&(e.data=za(i))),ms=!0,o){var N=function(e){var t=o[e],i=fn(t)?t.bind(n,n):fn(t.get)?t.get.bind(n,n):tn,a=!fn(t)&&fn(t.set)?t.set.bind(n):tn,s=Fl({get:i,set:a});Object.defineProperty(r,e,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})};for(var R in o)N(R)}if(l)for(var P in l)bs(l[P],r,n,P);if(u){var D=fn(u)?u.call(n):u;Reflect.ownKeys(D).forEach((e=>{Bs(e,D[e])}))}function z(e,t){cn(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&ys(d,e,"c"),z(ns,h),z(rs,f),z(is,p),z(as,v),z(Go,g),z(Ko,m),z(ds,T),z(cs,S),z(us,k),z(os,y),z(ss,w),z(ls,E),cn(C))if(C.length){var F=e.exposed||(e.exposed={});C.forEach((e=>{Object.defineProperty(F,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});x&&e.render===tn&&(e.render=x),null!=O&&(e.inheritAttrs=O),M&&(e.components=M),L&&(e.directives=L)}function ys(e,t,n){oo(cn(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function bs(e,t,n,r){var i=r.includes(".")?Wo(n,r):()=>n[r];if(pn(e)){var a=t[e];fn(a)&&$o(i,a)}else if(fn(e))$o(i,e.bind(n));else if(gn(e))if(cn(e))e.forEach((e=>bs(e,t,n,r)));else{var o=fn(e.handler)?e.handler.bind(n):t[e.handler];fn(o)&&$o(i,o,e)}}function ws(e){var t,n=e.type,{mixins:r,extends:i}=n,{mixins:a,optionsCache:o,config:{optionMergeStrategies:s}}=e.appContext,l=o.get(n);return l?t=l:a.length||r||i?(t={},a.length&&a.forEach((e=>xs(t,e,s,!0))),xs(t,n,s)):t=n,gn(n)&&o.set(n,t),t}function xs(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],{mixins:i,extends:a}=t;for(var o in a&&xs(e,a,n,!0),i&&i.forEach((t=>xs(e,t,n,!0))),t)if(r&&"expose"===o);else{var s=Ss[o]||n&&n[o];e[o]=s?s(e[o],t[o]):t[o]}return e}var Ss={data:ks,props:Os,emits:Os,methods:Cs,computed:Cs,beforeCreate:Es,created:Es,beforeMount:Es,mounted:Es,beforeUpdate:Es,updated:Es,beforeDestroy:Es,beforeUnmount:Es,destroyed:Es,unmounted:Es,activated:Es,deactivated:Es,errorCaptured:Es,serverPrefetch:Es,components:Cs,directives:Cs,watch:function(e,t){if(!e)return t;if(!t)return e;var n=on(Object.create(null),e);for(var r in t)n[r]=Es(e[r],t[r]);return n},provide:ks,inject:function(e,t){return Cs(Ts(e),Ts(t))}};function ks(e,t){return t?e?function(){return on(fn(e)?e.call(this,this):e,fn(t)?t.call(this,this):t)}:t:e}function Ts(e){if(cn(e)){for(var t={},n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Es(e,t){return e?[...new Set([].concat(e,t))]:t}function Cs(e,t){return e?on(Object.create(null),e,t):t}function Os(e,t){return e?cn(e)&&cn(t)?[...new Set([...e,...t])]:on(Object.create(null),gs(e),gs(null!=t?t:{})):t}function Ms(){return{app:null,config:{isNativeTag:nn,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}var Ls=0;function Is(e,t){return function(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;fn(n)||(n=on({},n)),null==r||gn(r)||(r=null);var i=Ms(),a=new WeakSet,o=!1,s=i.app={_uid:Ls++,_component:n,_props:r,_container:null,_context:i,_instance:null,version:jl,get config(){return i.config},set config(e){},use(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return a.has(e)||(e&&fn(e.install)?(a.add(e),e.install(s,...n)):fn(e)&&(a.add(e),e(s,...n))),s},mixin:e=>(i.mixins.includes(e)||i.mixins.push(e),s),component:(e,t)=>t?(i.components[e]=t,s):i.components[e],directive:(e,t)=>t?(i.directives[e]=t,s):i.directives[e],mount(a,l,u){if(!o){var c=vl(n,r);return c.appContext=i,!0===u?u="svg":!1===u&&(u=void 0),l&&t?t(c,a):e(c,a,u),o=!0,s._container=a,a.__vue_app__=s,zl(c.component)||c.component.proxy}},unmount(){o&&(e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(i.provides[e]=t,s),runWithContext(e){var t=As;As=s;try{return e()}finally{As=t}}};return s}}var As=null;function Bs(e,t){if(Cl){var n=Cl.provides,r=Cl.parent&&Cl.parent.provides;r===n&&(n=Cl.provides=Object.create(r)),n[e]=t}else;}function Ns(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=Cl||Mo;if(r||As){var i=r?null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:As._context.provides;if(i&&e in i)return i[e];if(arguments.length>1)return n&&fn(t)?t.call(r&&r.proxy):t}}function Rs(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i={},a={};for(var o in Bn(a,hl,1),e.propsDefaults=Object.create(null),Ps(e,t,i,a),e.propsOptions[0])o in i||(i[o]=void 0);n?e.props=r?i:$a(i,!1,ha,Aa,Ra):e.type.props?e.props=i:e.props=a,e.attrs=a}function Ps(e,t,n,r){var i,[a,o]=e.propsOptions,s=!1;if(t)for(var l in t)if(!Sn(l)){var u=t[l],c=void 0;a&&un(a,c=En(l))?o&&o.includes(c)?(i||(i={}))[c]=u:n[c]=u:Oo(e.emitsOptions,l)||l in r&&u===r[l]||(r[l]=u,s=!0)}if(o)for(var d=Ua(n),h=i||Qt,f=0;f<o.length;f++){var p=o[f];n[p]=Ds(a,d,p,h[p],e,!un(h,p))}return s}function Ds(e,t,n,r,i,a){var o=e[n];if(null!=o){var s=un(o,"default");if(s&&void 0===r){var l=o.default;if(o.type!==Function&&!o.skipFactory&&fn(l)){var{propsDefaults:u}=i;if(n in u)r=u[n];else{var c=Il(i);r=u[n]=l.call(null,t),c()}}else r=l}o[0]&&(a&&!s?r=!1:!o[1]||""!==r&&r!==On(n)||(r=!0))}return r}function zs(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t.propsCache,i=r.get(e);if(i)return i;var a=e.props,o={},s=[],l=!1;if(!fn(e)){var u=e=>{l=!0;var[n,r]=zs(e,t,!0);on(o,n),r&&s.push(...r)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!a&&!l)return gn(e)&&r.set(e,en),en;if(cn(a))for(var c=0;c<a.length;c++){var d=En(a[c]);Fs(d)&&(o[d]=Qt)}else if(a)for(var h in a){var f=En(h);if(Fs(f)){var p=a[h],v=o[f]=cn(p)||fn(p)?{type:p}:on({},p);if(v){var g=Vs(Boolean,v.type),m=Vs(String,v.type);v[0]=g>-1,v[1]=m<0||g<m,(g>-1||un(v,"default"))&&s.push(f)}}}var _=[o,s];return gn(e)&&r.set(e,_),_}function Fs(e){return"$"!==e[0]&&!Sn(e)}function $s(e){return null===e?"null":"function"==typeof e?e.name||"":"object"==typeof e&&e.constructor&&e.constructor.name||""}function js(e,t){return $s(e)===$s(t)}function Vs(e,t){return cn(t)?t.findIndex((t=>js(t,e))):fn(t)&&js(t,e)?0:-1}var Ws=e=>"_"===e[0]||"$stable"===e,Hs=e=>cn(e)?e.map(_l):[_l(e)],Us=(e,t,n)=>{if(t._n)return t;var r=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Mo;if(!t)return e;if(e._n)return e;var n=function(){n._d&&ul(-1);var r,i=Io(t);try{r=e(...arguments)}finally{Io(i),n._d&&ul(1)}return r};return n._n=!0,n._c=!0,n._d=!0,n}((function(){return Hs(t(...arguments))}),n);return r._c=!1,r},qs=(e,t,n)=>{var r=e._ctx;for(var i in e)if(!Ws(i)){var a=e[i];fn(a)?t[i]=Us(0,a,r):null!=a&&function(){var e=Hs(a);t[i]=()=>e}()}},Ys=(e,t)=>{var n=Hs(t);e.slots.default=()=>n},Xs=(e,t)=>{if(32&e.vnode.shapeFlag){var n=t._;n?(e.slots=Ua(t),Bn(t,"_",n)):qs(t,e.slots={})}else e.slots={},t&&Ys(e,t);Bn(e.slots,hl,1)},Zs=(e,t,n)=>{var{vnode:r,slots:i}=e,a=!0,o=Qt;if(32&r.shapeFlag){var s=t._;s?n&&1===s?a=!1:(on(i,t),n||1!==s||delete i._):(a=!t.$stable,qs(t,i)),o=t}else t&&(Ys(e,t),o={default:1});if(a)for(var l in i)Ws(l)||null!=o[l]||delete i[l]};function Gs(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(cn(e))e.forEach(((e,a)=>Gs(e,t&&(cn(t)?t[a]:t),n,r,i)));else if(!Xo(r)||i){var a=4&r.shapeFlag?zl(r.component)||r.component.proxy:r.el,o=i?null:a,{i:s,r:l}=e,u=t&&t.r,c=s.refs===Qt?s.refs={}:s.refs,d=s.setupState;if(null!=u&&u!==l&&(pn(u)?(c[u]=null,un(d,u)&&(d[u]=null)):Ja(u)&&(u.value=null)),fn(l))ao(l,s,12,[o,c]);else{var h=pn(l),f=Ja(l);if(h||f){var p=()=>{if(e.f){var t=h?un(d,l)?d[l]:c[l]:l.value;i?cn(t)&&sn(t,a):cn(t)?t.includes(a)||t.push(a):h?(c[l]=[a],un(d,l)&&(d[l]=c[l])):(l.value=[a],e.k&&(c[e.k]=l.value))}else h?(c[l]=o,un(d,l)&&(d[l]=o)):f&&(l.value=o,e.k&&(c[e.k]=o))};o?(p.id=-1,Ks(p,n)):p()}}}}var Ks=function(e,t){var n;t&&t.pendingBranch?cn(e)?t.effects.push(...e):t.effects.push(e):(cn(n=e)?fo.push(...n):po&&po.includes(n,n.allowRecurse?vo+1:vo)||fo.push(n),bo())};function Js(e){return function(e,t){Rn().__VUE__=!0;var n,r,{insert:i,remove:a,patchProp:o,createElement:s,createText:l,createComment:u,setText:c,setElementText:d,parentNode:h,nextSibling:f,setScopeId:p=tn,insertStaticContent:v}=e,g=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:void 0,s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:!!t.dynamicChildren;if(e!==t){e&&!dl(e,t)&&(r=H(e),F(e,i,a,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);var{type:u,ref:c,shapeFlag:d}=t;switch(u){case il:m(e,t,n,r);break;case al:_(e,t,n,r);break;case ol:null==e&&y(t,n,r,o);break;case rl:M(e,t,n,r,i,a,o,s,l);break;default:1&d?x(e,t,n,r,i,a,o,s,l):6&d?L(e,t,n,r,i,a,o,s,l):(64&d||128&d)&&u.process(e,t,n,r,i,a,o,s,l,Y)}null!=c&&i&&Gs(c,e&&e.ref,a,t||e,!t)}},m=(e,t,n,r)=>{if(null==e)i(t.el=l(t.children),n,r);else{var a=t.el=e.el;t.children!==e.children&&c(a,t.children)}},_=(e,t,n,r)=>{null==e?i(t.el=u(t.children||""),n,r):t.el=e.el},y=(e,t,n,r)=>{[e.el,e.anchor]=v(e.children,t,n,r,e.el,e.anchor)},b=(e,t,n)=>{for(var r,{el:a,anchor:o}=e;a&&a!==o;)r=f(a),i(a,t,n),a=r;i(o,t,n)},w=e=>{for(var t,{el:n,anchor:r}=e;n&&n!==r;)t=f(n),a(n),n=t;a(r)},x=(e,t,n,r,i,a,o,s,l)=>{"svg"===t.type?o="svg":"math"===t.type&&(o="mathml"),null==e?S(t,n,r,i,a,o,s,l):E(e,t,i,a,o,s,l)},S=(e,t,n,r,a,l,u,c)=>{var h,f,{props:p,shapeFlag:v,transition:g,dirs:m}=e;if(h=e.el=s(e.type,l,p&&p.is,p),8&v?d(h,e.children):16&v&&T(e.children,h,null,r,a,Qs(e,l),u,c),m&&qo(e,null,r,"created"),k(h,e,e.scopeId,u,r),p){for(var _ in p)"value"===_||Sn(_)||o(h,_,null,p[_],l,e.children,r,a,W);"value"in p&&o(h,"value",null,p.value,l),(f=p.onVnodeBeforeMount)&&xl(f,r,e)}Object.defineProperty(h,"__vueParentComponent",{value:r,enumerable:!1}),m&&qo(e,null,r,"beforeMount");var y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(a,g);y&&g.beforeEnter(h),i(h,t,n),((f=p&&p.onVnodeMounted)||y||m)&&Ks((()=>{f&&xl(f,r,e),y&&g.enter(h),m&&qo(e,null,r,"mounted")}),a)},k=(e,t,n,r,i)=>{if(n&&p(e,n),r)for(var a=0;a<r.length;a++)p(e,r[a]);if(i&&t===i.subTree){var o=i.vnode;k(e,o,o.scopeId,o.slotScopeIds,i.parent)}},T=function(e,t,n,r,i,a,o,s){for(var l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:0;l<e.length;l++){var u=e[l]=s?yl(e[l]):_l(e[l]);g(null,u,t,n,r,i,a,o,s)}},E=(e,t,n,r,i,a,s)=>{var l=t.el=e.el,{patchFlag:u,dynamicChildren:c,dirs:h}=t;u|=16&e.patchFlag;var f,p=e.props||Qt,v=t.props||Qt;if(n&&el(n,!1),(f=v.onVnodeBeforeUpdate)&&xl(f,n,t,e),h&&qo(t,e,n,"beforeUpdate"),n&&el(n,!0),c?C(e.dynamicChildren,c,l,n,r,Qs(t,i),a):s||R(e,t,l,null,n,r,Qs(t,i),a,!1),u>0){if(16&u)O(l,t,p,v,n,r,i);else if(2&u&&p.class!==v.class&&o(l,"class",null,v.class,i),4&u&&o(l,"style",p.style,v.style,i),8&u)for(var g=t.dynamicProps,m=0;m<g.length;m++){var _=g[m],y=p[_],b=v[_];b===y&&"value"!==_||o(l,_,y,b,i,e.children,n,r,W)}1&u&&e.children!==t.children&&d(l,t.children)}else s||null!=c||O(l,t,p,v,n,r,i);((f=v.onVnodeUpdated)||h)&&Ks((()=>{f&&xl(f,n,t,e),h&&qo(t,e,n,"updated")}),r)},C=(e,t,n,r,i,a,o)=>{for(var s=0;s<t.length;s++){var l=e[s],u=t[s],c=l.el&&(l.type===rl||!dl(l,u)||70&l.shapeFlag)?h(l.el):n;g(l,u,c,null,r,i,a,o,!0)}},O=(e,t,n,r,i,a,s)=>{if(n!==r){if(n!==Qt)for(var l in n)Sn(l)||l in r||o(e,l,n[l],null,s,t.children,i,a,W);for(var u in r)if(!Sn(u)){var c=r[u],d=n[u];c!==d&&"value"!==u&&o(e,u,d,c,s,t.children,i,a,W)}"value"in r&&o(e,"value",n.value,r.value,s)}},M=(e,t,n,r,a,o,s,u,c)=>{var d=t.el=e?e.el:l(""),h=t.anchor=e?e.anchor:l(""),{patchFlag:f,dynamicChildren:p,slotScopeIds:v}=t;v&&(u=u?u.concat(v):v),null==e?(i(d,n,r),i(h,n,r),T(t.children||[],n,h,a,o,s,u,c)):f>0&&64&f&&p&&e.dynamicChildren?(C(e.dynamicChildren,p,n,a,o,s,u),(null!=t.key||a&&t===a.subTree)&&tl(e,t,!0)):R(e,t,n,h,a,o,s,u,c)},L=(e,t,n,r,i,a,o,s,l)=>{t.slotScopeIds=s,null==e?512&t.shapeFlag?i.ctx.activate(t,n,r,o,l):I(t,n,r,i,a,o,l):A(e,t,l)},I=(e,t,n,r,i,a,o)=>{var s=e.component=function(e,t,n){var r=e.type,i=(t?t.appContext:e.appContext)||Sl,a={uid:kl++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new Ri(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:zs(r,i),emitsOptions:Co(r,i),emit:null,emitted:null,propsDefaults:Qt,inheritAttrs:r.inheritAttrs,ctx:Qt,data:Qt,props:Qt,attrs:Qt,slots:Qt,refs:Qt,setupState:Qt,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};a.ctx={_:a},a.root=t?t.root:a,a.emit=Eo.bind(null,a),e.ce&&e.ce(a);return a}(e,r,i);if(Zo(e)&&(s.ctx.renderer=Y),function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t&&El(t);var{props:n,children:r}=e.vnode,i=Bl(e);Rs(e,n,i,t),Xs(e,r);var a=i?function(e,t){var n=e.type;e.accessCache=Object.create(null),e.proxy=qa(new Proxy(e.ctx,vs));var{setup:r}=n;if(r){var i=e.setupContext=r.length>1?function(e){var t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(ea(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,a=Il(e);Wi();var o=ao(r,e,0,[e.props,i]);if(Hi(),a(),mn(o)){if(o.then(Al,Al),t)return o.then((n=>{Pl(e,n,t)})).catch((t=>{so(t,e,0)}));e.asyncDep=o}else Pl(e,o,t)}else Dl(e,t)}(e,t):void 0;t&&El(!1)}(s),s.asyncDep){if(i&&i.registerDep(s,B),!e.el){var l=s.subTree=vl(al);_(null,l,t,n)}}else B(s,e,t,n,i,a,o)},A=(e,t,n)=>{var r,i,a=t.component=e.component;if(function(e,t,n){var{props:r,children:i,component:a}=e,{props:o,children:s,patchFlag:l}=t,u=a.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!i&&!s||s&&s.$stable)||r!==o&&(r?!o||Ro(r,o,u):!!o);if(1024&l)return!0;if(16&l)return r?Ro(r,o,u):!!o;if(8&l)for(var c=t.dynamicProps,d=0;d<c.length;d++){var h=c[d];if(o[h]!==r[h]&&!Oo(u,h))return!0}return!1}(e,t,n)){if(a.asyncDep&&!a.asyncResolved)return void N(a,t,n);a.next=t,r=a.update,(i=co.indexOf(r))>ho&&co.splice(i,1),a.effect.dirty=!0,a.update()}else t.el=e.el,a.vnode=t},B=(e,t,n,i,a,o,s)=>{var l=()=>{if(e.isMounted){var{next:u,bu:c,u:d,parent:f,vnode:p}=e,v=nl(e);if(v)return u&&(u.el=p.el,N(e,u,s)),void v.asyncDep.then((()=>{e.isUnmounted||l()}));var m,_=u;el(e,!1),u?(u.el=p.el,N(e,u,s)):u=p,c&&An(c),(m=u.props&&u.props.onVnodeBeforeUpdate)&&xl(m,f,u,p),el(e,!0);var y=Ao(e),b=e.subTree;e.subTree=y,g(b,y,h(b.el),H(b),e,a,o),u.el=y.el,null===_&&function(e,t){for(var{vnode:n,parent:r}=e;r;){var i=r.subTree;if(i.suspense&&i.suspense.activeBranch===n&&(i.el=n.el),i!==n)break;(n=r.vnode).el=t,r=r.parent}}(e,y.el),d&&Ks(d,a),(m=u.props&&u.props.onVnodeUpdated)&&Ks((()=>xl(m,f,u,p)),a)}else{var w,{el:x,props:S}=t,{bm:k,m:T,parent:E}=e,C=Xo(t);if(el(e,!1),k&&An(k),!C&&(w=S&&S.onVnodeBeforeMount)&&xl(w,E,t),el(e,!0),x&&r){var O=()=>{e.subTree=Ao(e),r(x,e.subTree,e,a,null)};C?t.type.__asyncLoader().then((()=>!e.isUnmounted&&O())):O()}else{var M=e.subTree=Ao(e);g(null,M,n,i,e,a,o),t.el=M.el}if(T&&Ks(T,a),!C&&(w=S&&S.onVnodeMounted)){var L=t;Ks((()=>xl(w,E,L)),a)}(256&t.shapeFlag||E&&Xo(E.vnode)&&256&E.vnode.shapeFlag)&&e.a&&Ks(e.a,a),e.isMounted=!0,t=n=i=null}},u=e.effect=new Pi(l,tn,(()=>yo(c)),e.scope),c=e.update=()=>{u.dirty&&u.run()};c.id=e.uid,el(e,!0),c()},N=(e,t,n)=>{t.component=e;var r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,r){var{props:i,attrs:a,vnode:{patchFlag:o}}=e,s=Ua(i),[l]=e.propsOptions,u=!1;if(!(r||o>0)||16&o){var c;for(var d in Ps(e,t,i,a)&&(u=!0),s)t&&(un(t,d)||(c=On(d))!==d&&un(t,c))||(l?!n||void 0===n[d]&&void 0===n[c]||(i[d]=Ds(l,s,d,void 0,e,!0)):delete i[d]);if(a!==s)for(var h in a)t&&un(t,h)||(delete a[h],u=!0)}else if(8&o)for(var f=e.vnode.dynamicProps,p=0;p<f.length;p++){var v=f[p];if(!Oo(e.emitsOptions,v)){var g=t[v];if(l)if(un(a,v))g!==a[v]&&(a[v]=g,u=!0);else{var m=En(v);i[m]=Ds(l,s,m,g,e,!1)}else g!==a[v]&&(a[v]=g,u=!0)}}u&&ta(e,"set","$attrs")}(e,t.props,r,n),Zs(e,t.children,n),Wi(),wo(e),Hi()},R=function(e,t,n,r,i,a,o,s){var l=arguments.length>8&&void 0!==arguments[8]&&arguments[8],u=e&&e.children,c=e?e.shapeFlag:0,h=t.children,{patchFlag:f,shapeFlag:p}=t;if(f>0){if(128&f)return void D(u,h,n,r,i,a,o,s,l);if(256&f)return void P(u,h,n,r,i,a,o,s,l)}8&p?(16&c&&W(u,i,a),h!==u&&d(n,h)):16&c?16&p?D(u,h,n,r,i,a,o,s,l):W(u,i,a,!0):(8&c&&d(n,""),16&p&&T(h,n,r,i,a,o,s,l))},P=(e,t,n,r,i,a,o,s,l)=>{t=t||en;var u,c=(e=e||en).length,d=t.length,h=Math.min(c,d);for(u=0;u<h;u++){var f=t[u]=l?yl(t[u]):_l(t[u]);g(e[u],f,n,null,i,a,o,s,l)}c>d?W(e,i,a,!0,!1,h):T(t,n,r,i,a,o,s,l,h)},D=(e,t,n,r,i,a,o,s,l)=>{for(var u=0,c=t.length,d=e.length-1,h=c-1;u<=d&&u<=h;){var f=e[u],p=t[u]=l?yl(t[u]):_l(t[u]);if(!dl(f,p))break;g(f,p,n,null,i,a,o,s,l),u++}for(;u<=d&&u<=h;){var v=e[d],m=t[h]=l?yl(t[h]):_l(t[h]);if(!dl(v,m))break;g(v,m,n,null,i,a,o,s,l),d--,h--}if(u>d){if(u<=h)for(var _=h+1,y=_<c?t[_].el:r;u<=h;)g(null,t[u]=l?yl(t[u]):_l(t[u]),n,y,i,a,o,s,l),u++}else if(u>h)for(;u<=d;)F(e[u],i,a,!0),u++;else{var b,w=u,x=u,S=new Map;for(u=x;u<=h;u++){var k=t[u]=l?yl(t[u]):_l(t[u]);null!=k.key&&S.set(k.key,u)}var T=0,E=h-x+1,C=!1,O=0,M=new Array(E);for(u=0;u<E;u++)M[u]=0;for(u=w;u<=d;u++){var L=e[u];if(T>=E)F(L,i,a,!0);else{var I=void 0;if(null!=L.key)I=S.get(L.key);else for(b=x;b<=h;b++)if(0===M[b-x]&&dl(L,t[b])){I=b;break}void 0===I?F(L,i,a,!0):(M[I-x]=u+1,I>=O?O=I:C=!0,g(L,t[I],n,null,i,a,o,s,l),T++)}}var A=C?function(e){var t,n,r,i,a,o=e.slice(),s=[0],l=e.length;for(t=0;t<l;t++){var u=e[t];if(0!==u){if(e[n=s[s.length-1]]<u){o[t]=n,s.push(t);continue}for(r=0,i=s.length-1;r<i;)e[s[a=r+i>>1]]<u?r=a+1:i=a;u<e[s[r]]&&(r>0&&(o[t]=s[r-1]),s[r]=t)}}r=s.length,i=s[r-1];for(;r-- >0;)s[r]=i,i=o[i];return s}(M):en;for(b=A.length-1,u=E-1;u>=0;u--){var B=x+u,N=t[B],R=B+1<c?t[B+1].el:r;0===M[u]?g(null,N,n,R,i,a,o,s,l):C&&(b<0||u!==A[b]?z(N,n,R,2):b--)}}},z=function(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,{el:o,type:s,transition:l,children:u,shapeFlag:c}=e;if(6&c)z(e.component.subTree,t,n,r);else if(128&c)e.suspense.move(t,n,r);else if(64&c)s.move(e,t,n,Y);else if(s!==rl){if(s!==ol)if(2!==r&&1&c&&l)if(0===r)l.beforeEnter(o),i(o,t,n),Ks((()=>l.enter(o)),a);else{var{leave:d,delayLeave:h,afterLeave:f}=l,p=()=>i(o,t,n),v=()=>{d(o,(()=>{p(),f&&f()}))};h?h(o,p,v):v()}else i(o,t,n);else b(e,t,n)}else{i(o,t,n);for(var g=0;g<u.length;g++)z(u[g],t,n,r);i(e.anchor,t,n)}},F=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],{type:a,props:o,ref:s,children:l,dynamicChildren:u,shapeFlag:c,patchFlag:d,dirs:h}=e;if(null!=s&&Gs(s,null,n,e,!0),256&c)t.ctx.deactivate(e);else{var f,p=1&c&&h,v=!Xo(e);if(v&&(f=o&&o.onVnodeBeforeUnmount)&&xl(f,t,e),6&c)V(e.component,n,r);else{if(128&c)return void e.suspense.unmount(n,r);p&&qo(e,null,t,"beforeUnmount"),64&c?e.type.remove(e,t,n,i,Y,r):u&&(a!==rl||d>0&&64&d)?W(u,t,n,!1,!0):(a===rl&&384&d||!i&&16&c)&&W(l,t,n),r&&$(e)}(v&&(f=o&&o.onVnodeUnmounted)||p)&&Ks((()=>{f&&xl(f,t,e),p&&qo(e,null,t,"unmounted")}),n)}},$=e=>{var{type:t,el:n,anchor:r,transition:i}=e;if(t!==rl)if(t!==ol){var o=()=>{a(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){var{leave:s,delayLeave:l}=i,u=()=>s(n,o);l?l(e.el,o,u):u()}else o()}else w(e);else j(n,r)},j=(e,t)=>{for(var n;e!==t;)n=f(e),a(e),e=n;a(t)},V=(e,t,n)=>{var{bum:r,scope:i,update:a,subTree:o,um:s}=e;r&&An(r),i.stop(),a&&(a.active=!1,F(o,e,t,n)),s&&Ks(s,t),Ks((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},W=function(e,t,n){for(var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;a<e.length;a++)F(e[a],t,n,r,i)},H=e=>6&e.shapeFlag?H(e.component.subTree):128&e.shapeFlag?e.suspense.next():f(e.anchor||e.el),U=!1,q=(e,t,n)=>{if(null==e)t._vnode&&F(t._vnode,null,null,!0);else{var r=t.__vueParent;g(t._vnode||null,e,t,null,r,null,n)}U||(U=!0,wo(),U=!1),t._vnode=e},Y={p:g,um:F,m:z,r:$,mt:I,mc:T,pc:R,pbc:C,n:H,o:e};t&&([n,r]=t(Y));return{render:q,hydrate:n,createApp:Is(q,n)}}(e)}function Qs(e,t){var{type:n,props:r}=e;return"svg"===t&&"foreignObject"===n||"mathml"===t&&"annotation-xml"===n&&r&&r.encoding&&r.encoding.includes("html")?void 0:t}function el(e,t){var{effect:n,update:r}=e;n.allowRecurse=r.allowRecurse=t}function tl(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e.children,i=t.children;if(cn(r)&&cn(i))for(var a=0;a<r.length;a++){var o=r[a],s=i[a];1&s.shapeFlag&&!s.dynamicChildren&&((s.patchFlag<=0||32===s.patchFlag)&&((s=i[a]=yl(i[a])).el=o.el),n||tl(o,s)),s.type===il&&(s.el=o.el)}}function nl(e){var t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:nl(t)}var rl=Symbol.for("v-fgt"),il=Symbol.for("v-txt"),al=Symbol.for("v-cmt"),ol=Symbol.for("v-stc"),sl=null,ll=1;function ul(e){ll+=e}function cl(e){return!!e&&!0===e.__v_isVNode}function dl(e,t){return e.type===t.type&&e.key===t.key}var hl="__vInternal",fl=e=>{var{key:t}=e;return null!=t?t:null},pl=e=>{var{ref:t,ref_key:n,ref_for:r}=e;return"number"==typeof t&&(t=""+t),null!=t?pn(t)||Ja(t)||fn(t)?{i:Mo,r:t,k:n,f:!!r}:t:null};var vl=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];e&&e!==Po||(e=al);if(cl(e)){var o=gl(e,t,!0);return n&&bl(o,n),ll>0&&!a&&sl&&(6&o.shapeFlag?sl[sl.indexOf(e)]=o:sl.push(o)),o.patchFlag|=-2,o}s=e,fn(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?Ha(e)||hl in e?on({},e):e:null}(t);var{class:l,style:u}=t;l&&!pn(l)&&(t.class=jn(l)),gn(u)&&(Ha(u)&&!cn(u)&&(u=on({},u)),t.style=Pn(u))}var c=pn(e)?1:(e=>e.__isSuspense)(e)?128:(e=>e.__isTeleport)(e)?64:gn(e)?4:fn(e)?2:0;return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e===rl?0:1,o=arguments.length>6&&void 0!==arguments[6]&&arguments[6],s=arguments.length>7&&void 0!==arguments[7]&&arguments[7],l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&fl(t),ref:t&&pl(t),scopeId:Lo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Mo};return s?(bl(l,n),128&a&&e.normalize(l)):n&&(l.shapeFlag|=pn(n)?8:16),ll>0&&!o&&sl&&(l.patchFlag>0||6&a)&&32!==l.patchFlag&&sl.push(l),l}(e,t,n,r,i,c,a,!0)};function gl(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{props:r,ref:i,patchFlag:a,children:o}=e,s=t?wl(r||{},t):r;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&fl(s),ref:t&&t.ref?n&&i?cn(i)?i.concat(pl(t)):[i,pl(t)]:pl(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==rl?-1===a?16:16|a:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&gl(e.ssContent),ssFallback:e.ssFallback&&gl(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function ml(){return vl(il,null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)}function _l(e){return null==e||"boolean"==typeof e?vl(al):cn(e)?vl(rl,null,e.slice()):"object"==typeof e?yl(e):vl(il,null,String(e))}function yl(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:gl(e)}function bl(e,t){var n=0,{shapeFlag:r}=e;if(null==t)t=null;else if(cn(t))n=16;else if("object"==typeof t){if(65&r){var i=t.default;return void(i&&(i._c&&(i._d=!1),bl(e,i()),i._c&&(i._d=!0)))}n=32;var a=t._;a||hl in t?3===a&&Mo&&(1===Mo.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Mo}else fn(t)?(t={default:t,_ctx:Mo},n=32):(t=String(t),64&r?(n=16,t=[ml(t)]):n=8);e.children=t,e.shapeFlag|=n}function wl(){for(var e={},t=0;t<arguments.length;t++){var n=t<0||arguments.length<=t?void 0:arguments[t];for(var r in n)if("class"===r)e.class!==n.class&&(e.class=jn([e.class,n.class]));else if("style"===r)e.style=Pn([e.style,n.style]);else if(rn(r)){var i=e[r],a=n[r];!a||i===a||cn(i)&&i.includes(a)||(e[r]=i?[].concat(i,a):a)}else""!==r&&(e[r]=n[r])}return e}function xl(e,t,n){oo(e,t,7,[n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:null])}var Sl=Ms(),kl=0;var Tl,El,Cl=null,Ol=()=>Cl||Mo,Ml=Rn(),Ll=(e,t)=>{var n;return(n=Ml[e])||(n=Ml[e]=[]),n.push(t),e=>{n.length>1?n.forEach((t=>t(e))):n[0](e)}};Tl=Ll("__VUE_INSTANCE_SETTERS__",(e=>Cl=e)),El=Ll("__VUE_SSR_SETTERS__",(e=>Rl=e));var Il=e=>{var t=Cl;return Tl(e),e.scope.on(),()=>{e.scope.off(),Tl(t)}},Al=()=>{Cl&&Cl.scope.off(),Tl(null)};function Bl(e){return 4&e.vnode.shapeFlag}var Nl,Rl=!1;function Pl(e,t,n){fn(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:gn(t)&&(e.setupState=io(t)),Dl(e,n)}function Dl(e,t,n){var r=e.type;if(!e.render){if(!t&&Nl&&!r.render){var i=r.template||ws(e).template;if(i){var{isCustomElement:a,compilerOptions:o}=e.appContext.config,{delimiters:s,compilerOptions:l}=r,u=on(on({isCustomElement:a,delimiters:s},o),l);r.render=Nl(i,u)}}e.render=r.render||tn}var c=Il(e);Wi();try{_s(e)}finally{Hi(),c()}}function zl(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(io(qa(e.exposed)),{get:(t,n)=>n in t?t[n]:n in fs?fs[n](e):void 0,has:(e,t)=>t in e||t in fs}))}var Fl=(e,t)=>{var n=function(e,t){var n,r,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=fn(e);return a?(n=e,r=tn):(n=e.get,r=e.set),new Za(n,r,a||!r,i)}(e,t,Rl);return n};function $l(e,t,n){var r=arguments.length;return 2===r?gn(t)&&!cn(t)?cl(t)?vl(e,null,[t]):vl(e,t):vl(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&cl(n)&&(n=[n]),vl(e,t,n))}var jl="3.4.21",Vl="undefined"!=typeof document?document:null,Wl=Vl&&Vl.createElement("template"),Hl={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{var t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{var i="svg"===t?Vl.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Vl.createElementNS("http://www.w3.org/1998/Math/MathML",e):Vl.createElement(e,n?{is:n}:void 0);return"select"===e&&r&&null!=r.multiple&&i.setAttribute("multiple",r.multiple),i},createText:e=>Vl.createTextNode(e),createComment:e=>Vl.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Vl.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,i,a){var o=n?n.previousSibling:t.lastChild;if(i&&(i===a||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),i!==a&&(i=i.nextSibling););else{Wl.innerHTML="svg"===r?"<svg>".concat(e,"</svg>"):"mathml"===r?"<math>".concat(e,"</math>"):e;var s=Wl.content;if("svg"===r||"mathml"===r){for(var l=s.firstChild;l.firstChild;)s.appendChild(l.firstChild);s.removeChild(l)}t.insertBefore(s,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ul=Symbol("_vtc");var ql=Symbol("_vod"),Yl=Symbol("_vsh"),Xl={beforeMount(e,t,n){var{value:r}=t,{transition:i}=n;e[ql]="none"===e.style.display?"":e.style.display,i&&r?i.beforeEnter(e):Zl(e,r)},mounted(e,t,n){var{value:r}=t,{transition:i}=n;i&&r&&i.enter(e)},updated(e,t,n){var{value:r,oldValue:i}=t,{transition:a}=n;!r!=!i&&(a?r?(a.beforeEnter(e),Zl(e,!0),a.enter(e)):a.leave(e,(()=>{Zl(e,!1)})):Zl(e,r))},beforeUnmount(e,t){var{value:n}=t;Zl(e,n)}};function Zl(e,t){e.style.display=t?e[ql]:"none",e[Yl]=!t}var Gl=Symbol(""),Kl=/(^|;)\s*display\s*:/;var Jl=/\s*!important$/;function Ql(e,t,n){if(cn(n))n.forEach((n=>Ql(e,t,n)));else if(null==n&&(n=""),n=normalizeStyleValue(n),t.startsWith("--"))e.setProperty(t,n);else{var r=normalizeStyleName(e,t);Jl.test(n)?e.setProperty(On(r),n.replace(Jl,""),"important"):e[r]=n}}var eu="http://www.w3.org/1999/xlink";var tu=Symbol("_vei");function nu(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=e[tu]||(e[tu]={}),o=a[t];if(r&&o)o.value=r;else{var[s,l]=function(e){var t;if(ru.test(e)){var n;for(t={};n=e.match(ru);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}var r=":"===e[2]?e.slice(3):On(e.slice(2));return[r,t]}(t);if(r){var u=a[t]=function(e,t){var n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();oo(function(e,t){if(cn(t)){var n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=ou(),n}(r,i);!function(e,t,n,r){e.addEventListener(t,n,r)}(e,s,u,l)}else o&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,s,o,l),a[t]=void 0)}}var ru=/(?:Once|Passive|Capture)$/;var iu=0,au=Promise.resolve(),ou=()=>iu||(au.then((()=>iu=0)),iu=Date.now());var su=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;var lu,uu=["ctrl","shift","alt","meta"],cu={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>uu.some((n=>e["".concat(n,"Key")]&&!t.includes(n)))},du=(e,t)=>{var n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=function(n){for(var r=0;r<t.length;r++){var i=cu[t[r]];if(i&&i(n,t))return}for(var a=arguments.length,o=new Array(a>1?a-1:0),s=1;s<a;s++)o[s-1]=arguments[s];return e(n,...o)})},hu=on({patchProp:(e,t,n,r,i,a,o,s,l)=>{var u="svg"===i;"class"===t?function(e,t,n){var r=e[Ul];r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,r,u):"style"===t?function(e,t,n){var r=e.style,i=pn(n),a=!1;if(n&&!i){if(t)if(pn(t))for(var o of t.split(";")){var s=o.slice(0,o.indexOf(":")).trim();null==n[s]&&Ql(r,s,"")}else for(var l in t)null==n[l]&&Ql(r,l,"");for(var u in n)"display"===u&&(a=!0),Ql(r,u,n[u])}else if(i){if(t!==n){var c=r[Gl];c&&(n+=";"+c),r.cssText=normalizeStyleValue(n),a=Kl.test(n)}}else t&&e.removeAttribute("style");ql in e&&(e[ql]=a?r.display:"",e[Yl]&&(r.display="none"))}(e,n,r):rn(t)?an(t)||nu(e,t,n,r,o):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,r){if(r)return"innerHTML"===t||"textContent"===t||!!(t in e&&su(t)&&fn(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){var i=e.tagName;if("IMG"===i||"VIDEO"===i||"CANVAS"===i||"SOURCE"===i)return!1}if(su(t)&&pn(n))return!1;return t in e}(e,t,r,u))?function(e,t,n,r,i,a,o){if("innerHTML"===t||"textContent"===t)return r&&o(r,i,a),void(e[t]=null==n?"":n);var s=e.tagName;if("value"===t&&"PROGRESS"!==s&&!s.includes("-")){var l=null==n?"":n;return("OPTION"===s?e.getAttribute("value")||"":e.value)===l&&"_value"in e||(e.value=l),null==n&&e.removeAttribute(t),void(e._value=n)}var u=!1;if(""===n||null==n){var c=typeof e[t];"boolean"===c?n=Un(n):null==n&&"string"===c?(n="",u=!0):"number"===c&&(n=0,u=!0)}try{e[t]=n}catch(d){}u&&e.removeAttribute(t)}(e,t,r,a,o,s,l):("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),function(e,t,n,r,i){if(r&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(eu,t.slice(6,t.length)):e.setAttributeNS(eu,t,n);else{var a=Hn(t);null==n||a&&!Un(n)?e.removeAttribute(t):e.setAttribute(t,a?"":n)}}(e,t,r,u))}},Hl);var fu=function(){var e=(lu||(lu=Js(hu))).createApp(...arguments),{mount:t}=e;return e.mount=n=>{var r=function(e){if(pn(e)){return document.querySelector(e)}return e}(n);if(r){var i=e._component;fn(i)||i.render||i.template||(i.template=r.innerHTML),r.innerHTML="";var a=t(r,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),a}},e};var pu,vu,gu=["top","left","right","bottom"],mu={};function _u(){return vu="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function yu(){if(vu="string"==typeof vu?vu:_u()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(s){}var r=document.createElement("div");i(r,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),gu.forEach((function(e){o(r,e)})),document.body.appendChild(r),a(),pu=!0}else gu.forEach((function(e){mu[e]=0}));function i(e,t){var n=e.style;Object.keys(t).forEach((function(e){var r=t[e];n[e]=r}))}function a(t){t?e.push(t):e.forEach((function(e){e()}))}function o(e,n){var r=document.createElement("div"),o=document.createElement("div"),s=document.createElement("div"),l=document.createElement("div"),u={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:vu+"(safe-area-inset-"+n+")"};i(r,u),i(o,u),i(s,{transition:"0s",animation:"none",width:"400px",height:"400px"}),i(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),r.appendChild(s),o.appendChild(l),e.appendChild(r),e.appendChild(o),a((function(){r.scrollTop=o.scrollTop=1e4;var e=r.scrollTop,i=o.scrollTop;function a(){this.scrollTop!==(this===r?e:i)&&(r.scrollTop=o.scrollTop=1e4,e=r.scrollTop,i=o.scrollTop,function(e){wu.length||setTimeout((function(){var e={};wu.forEach((function(t){e[t]=mu[t]})),wu.length=0,xu.forEach((function(t){t(e)}))}),0);wu.push(e)}(n))}r.addEventListener("scroll",a,t),o.addEventListener("scroll",a,t)}));var c=getComputedStyle(r);Object.defineProperty(mu,n,{configurable:!0,get:function(){return parseFloat(c.paddingBottom)}})}}function bu(e){return pu||yu(),mu[e]}var wu=[];var xu=[];const Su=e({get support(){return 0!=("string"==typeof vu?vu:_u()).length},get top(){return bu("top")},get left(){return bu("left")},get right(){return bu("right")},get bottom(){return bu("bottom")},onChange:function(e){_u()&&(pu||yu(),"function"==typeof e&&xu.push(e))},offChange:function(e){var t=xu.indexOf(e);t>=0&&xu.splice(t,1)}});var ku=du((()=>{}),["prevent"]);function Tu(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function Eu(){var e=Tu(document.documentElement.style,"--window-top");return e?e+Su.top:0}function Cu(e){return Symbol(e)}function Ou(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function Mu(e){if(arguments.length>1&&void 0!==arguments[1]&&arguments[1])return function(e){if(!Ou(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>uni.upx2px(parseFloat(t))+"px"))}(e);if(pn(e)){var t=parseInt(e)||0;return Ou(e)?uni.upx2px(t):t}return e}var Lu,Iu="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z";function Au(e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:27;return vl("svg",{width:t,height:t,viewBox:"0 0 32 32"},[vl("path",{d:e,fill:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"#000"},null,8,["d","fill"])],8,["width","height"])}function Bu(){return Nu()}function Nu(){return window.__id__||(window.__id__=plus.webview.currentWebview().id),parseInt(window.__id__)}function Ru(e){e.preventDefault()}var Pu,Du,zu,Fu,$u,ju=0;function Vu(e){var{onPageScroll:t,onReachBottom:n,onReachBottomDistance:r}=e,i=!1,a=!1,o=!0,s=()=>{function e(){if((()=>{var{scrollHeight:e}=document.documentElement,t=window.innerHeight,n=window.scrollY,i=n>0&&e>t&&n+t+r>=e,o=Math.abs(e-ju)>r;return!i||a&&!o?(!i&&a&&(a=!1),!1):(ju=e,a=!0,!0)})())return n&&n(),o=!1,setTimeout((function(){o=!0}),350),!0}t&&t(window.pageYOffset),n&&o&&(e()||(Lu=setTimeout(e,300))),i=!1};return function(){clearTimeout(Lu),i||requestAnimationFrame(s),i=!0}}function Wu(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Wu(e,t.slice(2));for(var n=t.split("/"),r=n.length,i=0;i<r&&".."===n[i];i++);n.splice(0,i),t=n.join("/");var a=e.length>0?e.split("/"):[];return a.splice(a.length-i-1,i+1),hr(a.concat(n).join("/"))}function Hu(){return"object"==typeof window&&"object"==typeof navigator&&"object"==typeof document?"webview":"v8"}function Uu(){return Pu.webview.currentWebview().id}var qu={};function Yu(e){var t=e.data&&e.data.__message;if(t&&t.__page){var n=t.__page,r=qu[n];r&&r(t),t.keep||delete qu[n]}}class Xu{constructor(e){this.webview=e}sendMessage(e){var t=JSON.parse(JSON.stringify({__message:{data:e}})),n=this.webview.id;zu?new zu(n).postMessage(t):Pu.webview.postMessageToUniNView&&Pu.webview.postMessageToUniNView(t,n)}close(){this.webview.close()}}function Zu(e){var{context:t={},url:n,data:r={},style:i={},onMessage:a,onClose:o}=e,s=__uniConfig.darkmode;Pu=t.plus||plus,Du=t.weex||("object"==typeof weex?weex:null),zu=t.BroadcastChannel||("object"==typeof BroadcastChannel?BroadcastChannel:null);var l="page".concat(Date.now());!1!==(i=on({},i)).titleNView&&"none"!==i.titleNView&&(i.titleNView=on({autoBackButton:!0,titleSize:"17px"},i.titleNView));var u={top:0,bottom:0,usingComponents:{},popGesture:"close",scrollIndicator:"none",animationType:"pop-in",animationDuration:200,uniNView:{path:"/".concat(n,".js"),defaultFontSize:16,viewport:Pu.screen.resolutionWidth}};i=on(u,i);var c=Pu.webview.create("",l,i,{extras:{from:Uu(),runtime:Hu(),data:on({},r,{darkmode:s}),useGlobalEvent:!zu}});return c.addEventListener("close",o),function(e,t){"v8"===Hu()?zu?(Fu&&Fu.close(),(Fu=new zu(Uu())).onmessage=Yu):$u||($u=Du.requireModule("globalEvent")).addEventListener("plusMessage",Yu):window.__plusMessage=Yu,qu[e]=t}(l,(e=>{fn(a)&&a(e.data),e.keep||c.close("auto")})),c.show(i.animationType,i.animationDuration),new Xu(c)}class Gu{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=e.$el,this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(this.$el&&e){var t=Qu(this.$el.querySelector(e));if(t)return Ku(t)}}selectAllComponents(e){if(!this.$el||!e)return[];for(var t=[],n=this.$el.querySelectorAll(e),r=0;r<n.length;r++){var i=Qu(n[r]);i&&t.push(Ku(i))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){var{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){var{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){var t="";if(!e||pn(e))return t;for(var n in e){var r=e[n],i=n.startsWith("--")?n:On(n);(pn(r)||"number"==typeof r)&&(t+="".concat(i,":").concat(r,";"))}return t}(e))}setStyle(e){return this.$el&&e?(pn(e)&&(e=$n(e)),wn(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;var t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;var{__wxsAddClass:t}=this.$el;if(t){var n=t.indexOf(e);n>-1&&t.splice(n,1)}var r=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===r.indexOf(e)&&(r.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.$vm[e];fn(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&UniViewJSBridge.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){var t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Ku(e){if(e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Gu(e)),e.$el.__wxsComponentDescriptor}function Ju(e,t){return Ku(e)}function Qu(e){if(e)return ec(e)}function ec(e){return e.__wxsVm||(e.__wxsVm={ownerId:e.__ownerId,$el:e,$emit(){},$forceUpdate(){var t,n,{__wxsStyle:r,__wxsAddClass:i,__wxsRemoveClass:a,__wxsStyleChanged:o,__wxsClassChanged:s}=e;o&&(e.__wxsStyleChanged=!1,r&&(n=()=>{Object.keys(r).forEach((t=>{e.style[t]=r[t]}))})),s&&(e.__wxsClassChanged=!1,t=()=>{a&&a.forEach((t=>{e.classList.remove(t)})),i&&i.forEach((t=>{e.classList.add(t)}))}),requestAnimationFrame((()=>{t&&t(),n&&n()}))}})}var tc=e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent,nc=e=>"click"===e.type,rc=e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type),ic=e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0;function ac(e,t,n){var{currentTarget:r}=e;if(!(e instanceof Event&&r instanceof HTMLElement))return[e];var i=0!==r.tagName.indexOf("UNI-"),a=oc(e,i);if(nc(e))!function(e,t){var{x:n,y:r}=t,i=Eu();e.detail={x:n,y:r-i},e.touches=e.changedTouches=[sc(t,i)]}(a,e);else if(rc(e))!function(e,t){var n=Eu();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[sc(t,n)]}(a,e);else if(ic(e)){var o=Eu();a.touches=lc(e.touches,o),a.changedTouches=lc(e.changedTouches,o)}else if(tc(e)){["key","code"].forEach((t=>{Object.defineProperty(a,t,{get:()=>e[t]})}))}return[a]}function oc(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{type:r,timeStamp:i,target:a,currentTarget:o}=e;t=br(n?a:function(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}(a));var s={type:r,timeStamp:i,target:t,detail:{},currentTarget:br(o)};return e._stopped&&(s._stopped=!0),e.type.startsWith("touch")&&(s.touches=e.touches,s.changedTouches=e.changedTouches),s}function sc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function lc(e,t){for(var n=[],r=0;r<e.length;r++){var{identifier:i,pageX:a,pageY:o,clientX:s,clientY:l,force:u}=e[r];n.push({identifier:i,pageX:a,pageY:o-t,clientX:s,clientY:l-t,force:u||0})}return n}var uc="vdSync",cc="onWebviewReady",dc=0,hc="setLocale",fc=on(wi,{publishHandler:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=Nu()+"";plus.webview.postMessageToUniNView({type:"subscribeHandler",args:{type:e,data:t,pageId:n}},"__uniapp__service")}});function pc(e,t,n,r){if(r&&r.beforeInvoke){var i=r.beforeInvoke(t);if(pn(i))return i}var a=function(e,t){var n=e[0];if(t&&t.formatArgs&&(wn(t.formatArgs)||!wn(n)))for(var r=t.formatArgs,i=Object.keys(r),a=0;a<i.length;a++){var o=i[a],s=r[o];if(fn(s)){var l=s(e[0][o],n);if(pn(l))return l}else un(n,o)||(n[o]=s)}}(t,r);if(a)return a}function vc(e,t,n,r){return function(e,t,n,r){return function(){for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];var a=pc(0,n,0,r);if(a)throw new Error(a);return t.apply(null,n)}}(0,t,0,r)}function gc(){if("undefined"!=typeof __SYSTEM_INFO__)return window.__SYSTEM_INFO__;var{resolutionWidth:e}=plus.screen.getCurrentSize()||{resolutionWidth:0};return{platform:(plus.os.name||"").toLowerCase(),pixelRatio:plus.screen.scale,windowWidth:Math.round(e)}}function mc(e){if(0===e.indexOf("//"))return"https:"+e;if(tr.test(e)||nr.test(e))return e;if(function(e){if(0===e.indexOf("_www")||0===e.indexOf("_doc")||0===e.indexOf("_documents")||0===e.indexOf("_downloads"))return!0;return!1}(e))return"file://"+_c(e);var t="file://"+_c("_www");if(0===e.indexOf("/"))return e.startsWith("/storage/")||e.startsWith("/sdcard/")||e.includes("/Containers/Data/Application/")?"file://"+e:t+e;if(0===e.indexOf("../")||0===e.indexOf("./")){if("string"==typeof __id__)return t+Wu(hr(__id__),e);var n=window.__PAGE_INFO__;if(n)return t+Wu(hr(n.route),e)}return e}var _c=function(e){var t=Object.create(null);return n=>t[n]||(t[n]=e(n))}((e=>plus.io.convertLocalFileSystemURL(e).replace(/^\/?apps\//,"/android_asset/apps/").replace(/\/$/,"")));var yc=0;var bc="_doc/uniapp_temp";function wc(e){return function(e){return new Promise((function(t,n){0===e.indexOf("http://")||0===e.indexOf("https://")?plus.downloader.createDownload(e,{filename:"".concat(bc,"/download/")},(function(e,r){200===r?t(e.filename):n(new Error("network fail"))})).start():t(e)}))}(e).then((function(e){var t,n=window;return n.webkit&&n.webkit.messageHandlers?(t=e,new Promise((function(e,n){function r(){var r=new plus.nativeObj.Bitmap("bitmap_".concat(Date.now(),"_").concat(Math.random(),"}"));r.load(t,(function(){e(r.toBase64Data()),r.clear()}),(function(e){r.clear(),n(e)}))}plus.io.resolveLocalFileSystemURL(t,(function(t){t.file((function(t){var n=new plus.io.FileReader;n.onload=function(t){e(t.target.result)},n.onerror=r,n.readAsDataURL(t)}),r)}),r)}))):plus.io.convertLocalFileSystemURL(e)}))}var xc={};!function(e){var t="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var i in r)n(r,i)&&(e[i]=r[i])}}return e},e.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var r={arraySet:function(e,t,n,r,i){if(t.subarray&&e.subarray)e.set(t.subarray(n,n+r),i);else for(var a=0;a<r;a++)e[i+a]=t[n+a]},flattenChunks:function(e){var t,n,r,i,a,o;for(r=0,t=0,n=e.length;t<n;t++)r+=e[t].length;for(o=new Uint8Array(r),i=0,t=0,n=e.length;t<n;t++)a=e[t],o.set(a,i),i+=a.length;return o}},i={arraySet:function(e,t,n,r,i){for(var a=0;a<r;a++)e[i+a]=t[n+a]},flattenChunks:function(e){return[].concat.apply([],e)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,r)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,i))},e.setTyped(t)}(xc);var Sc={},kc={},Tc={},Ec=xc,Cc=0,Oc=1;function Mc(e){for(var t=e.length;--t>=0;)e[t]=0}var Lc=0,Ic=29,Ac=256,Bc=Ac+1+Ic,Nc=30,Rc=19,Pc=2*Bc+1,Dc=15,zc=16,Fc=7,$c=256,jc=16,Vc=17,Wc=18,Hc=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],Uc=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],qc=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],Yc=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Xc=new Array(2*(Bc+2));Mc(Xc);var Zc=new Array(2*Nc);Mc(Zc);var Gc=new Array(512);Mc(Gc);var Kc=new Array(256);Mc(Kc);var Jc=new Array(Ic);Mc(Jc);var Qc,ed,td,nd=new Array(Nc);function rd(e,t,n,r,i){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=e&&e.length}function id(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function ad(e){return e<256?Gc[e]:Gc[256+(e>>>7)]}function od(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function sd(e,t,n){e.bi_valid>zc-n?(e.bi_buf|=t<<e.bi_valid&65535,od(e,e.bi_buf),e.bi_buf=t>>zc-e.bi_valid,e.bi_valid+=n-zc):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)}function ld(e,t,n){sd(e,n[2*t],n[2*t+1])}function ud(e,t){var n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}function cd(e,t,n){var r,i,a=new Array(Dc+1),o=0;for(r=1;r<=Dc;r++)a[r]=o=o+n[r-1]<<1;for(i=0;i<=t;i++){var s=e[2*i+1];0!==s&&(e[2*i]=ud(a[s]++,s))}}function dd(e){var t;for(t=0;t<Bc;t++)e.dyn_ltree[2*t]=0;for(t=0;t<Nc;t++)e.dyn_dtree[2*t]=0;for(t=0;t<Rc;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*$c]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function hd(e){e.bi_valid>8?od(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function fd(e,t,n,r){var i=2*t,a=2*n;return e[i]<e[a]||e[i]===e[a]&&r[t]<=r[n]}function pd(e,t,n){for(var r=e.heap[n],i=n<<1;i<=e.heap_len&&(i<e.heap_len&&fd(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!fd(t,r,e.heap[i],e.depth));)e.heap[n]=e.heap[i],n=i,i<<=1;e.heap[n]=r}function vd(e,t,n){var r,i,a,o,s=0;if(0!==e.last_lit)do{r=e.pending_buf[e.d_buf+2*s]<<8|e.pending_buf[e.d_buf+2*s+1],i=e.pending_buf[e.l_buf+s],s++,0===r?ld(e,i,t):(ld(e,(a=Kc[i])+Ac+1,t),0!==(o=Hc[a])&&sd(e,i-=Jc[a],o),ld(e,a=ad(--r),n),0!==(o=Uc[a])&&sd(e,r-=nd[a],o))}while(s<e.last_lit);ld(e,$c,t)}function gd(e,t){var n,r,i,a=t.dyn_tree,o=t.stat_desc.static_tree,s=t.stat_desc.has_stree,l=t.stat_desc.elems,u=-1;for(e.heap_len=0,e.heap_max=Pc,n=0;n<l;n++)0!==a[2*n]?(e.heap[++e.heap_len]=u=n,e.depth[n]=0):a[2*n+1]=0;for(;e.heap_len<2;)a[2*(i=e.heap[++e.heap_len]=u<2?++u:0)]=1,e.depth[i]=0,e.opt_len--,s&&(e.static_len-=o[2*i+1]);for(t.max_code=u,n=e.heap_len>>1;n>=1;n--)pd(e,a,n);i=l;do{n=e.heap[1],e.heap[1]=e.heap[e.heap_len--],pd(e,a,1),r=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=r,a[2*i]=a[2*n]+a[2*r],e.depth[i]=(e.depth[n]>=e.depth[r]?e.depth[n]:e.depth[r])+1,a[2*n+1]=a[2*r+1]=i,e.heap[1]=i++,pd(e,a,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var n,r,i,a,o,s,l=t.dyn_tree,u=t.max_code,c=t.stat_desc.static_tree,d=t.stat_desc.has_stree,h=t.stat_desc.extra_bits,f=t.stat_desc.extra_base,p=t.stat_desc.max_length,v=0;for(a=0;a<=Dc;a++)e.bl_count[a]=0;for(l[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<Pc;n++)(a=l[2*l[2*(r=e.heap[n])+1]+1]+1)>p&&(a=p,v++),l[2*r+1]=a,r>u||(e.bl_count[a]++,o=0,r>=f&&(o=h[r-f]),s=l[2*r],e.opt_len+=s*(a+o),d&&(e.static_len+=s*(c[2*r+1]+o)));if(0!==v){do{for(a=p-1;0===e.bl_count[a];)a--;e.bl_count[a]--,e.bl_count[a+1]+=2,e.bl_count[p]--,v-=2}while(v>0);for(a=p;0!==a;a--)for(r=e.bl_count[a];0!==r;)(i=e.heap[--n])>u||(l[2*i+1]!==a&&(e.opt_len+=(a-l[2*i+1])*l[2*i],l[2*i+1]=a),r--)}}(e,t),cd(a,u,e.bl_count)}function md(e,t,n){var r,i,a=-1,o=t[1],s=0,l=7,u=4;for(0===o&&(l=138,u=3),t[2*(n+1)+1]=65535,r=0;r<=n;r++)i=o,o=t[2*(r+1)+1],++s<l&&i===o||(s<u?e.bl_tree[2*i]+=s:0!==i?(i!==a&&e.bl_tree[2*i]++,e.bl_tree[2*jc]++):s<=10?e.bl_tree[2*Vc]++:e.bl_tree[2*Wc]++,s=0,a=i,0===o?(l=138,u=3):i===o?(l=6,u=3):(l=7,u=4))}function _d(e,t,n){var r,i,a=-1,o=t[1],s=0,l=7,u=4;for(0===o&&(l=138,u=3),r=0;r<=n;r++)if(i=o,o=t[2*(r+1)+1],!(++s<l&&i===o)){if(s<u)do{ld(e,i,e.bl_tree)}while(0!=--s);else 0!==i?(i!==a&&(ld(e,i,e.bl_tree),s--),ld(e,jc,e.bl_tree),sd(e,s-3,2)):s<=10?(ld(e,Vc,e.bl_tree),sd(e,s-3,3)):(ld(e,Wc,e.bl_tree),sd(e,s-11,7));s=0,a=i,0===o?(l=138,u=3):i===o?(l=6,u=3):(l=7,u=4)}}Mc(nd);var yd=!1;function bd(e,t,n,r){sd(e,(Lc<<1)+(r?1:0),3),function(e,t,n,r){hd(e),r&&(od(e,n),od(e,~n)),Ec.arraySet(e.pending_buf,e.window,t,n,e.pending),e.pending+=n}(e,t,n,!0)}Tc._tr_init=function(e){yd||(!function(){var e,t,n,r,i,a=new Array(Dc+1);for(n=0,r=0;r<Ic-1;r++)for(Jc[r]=n,e=0;e<1<<Hc[r];e++)Kc[n++]=r;for(Kc[n-1]=r,i=0,r=0;r<16;r++)for(nd[r]=i,e=0;e<1<<Uc[r];e++)Gc[i++]=r;for(i>>=7;r<Nc;r++)for(nd[r]=i<<7,e=0;e<1<<Uc[r]-7;e++)Gc[256+i++]=r;for(t=0;t<=Dc;t++)a[t]=0;for(e=0;e<=143;)Xc[2*e+1]=8,e++,a[8]++;for(;e<=255;)Xc[2*e+1]=9,e++,a[9]++;for(;e<=279;)Xc[2*e+1]=7,e++,a[7]++;for(;e<=287;)Xc[2*e+1]=8,e++,a[8]++;for(cd(Xc,Bc+1,a),e=0;e<Nc;e++)Zc[2*e+1]=5,Zc[2*e]=ud(e,5);Qc=new rd(Xc,Hc,Ac+1,Bc,Dc),ed=new rd(Zc,Uc,0,Nc,Dc),td=new rd(new Array(0),qc,0,Rc,Fc)}(),yd=!0),e.l_desc=new id(e.dyn_ltree,Qc),e.d_desc=new id(e.dyn_dtree,ed),e.bl_desc=new id(e.bl_tree,td),e.bi_buf=0,e.bi_valid=0,dd(e)},Tc._tr_stored_block=bd,Tc._tr_flush_block=function(e,t,n,r){var i,a,o=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return Cc;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return Oc;for(t=32;t<Ac;t++)if(0!==e.dyn_ltree[2*t])return Oc;return Cc}(e)),gd(e,e.l_desc),gd(e,e.d_desc),o=function(e){var t;for(md(e,e.dyn_ltree,e.l_desc.max_code),md(e,e.dyn_dtree,e.d_desc.max_code),gd(e,e.bl_desc),t=Rc-1;t>=3&&0===e.bl_tree[2*Yc[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(a=e.static_len+3+7>>>3)<=i&&(i=a)):i=a=n+5,n+4<=i&&-1!==t?bd(e,t,n,r):4===e.strategy||a===i?(sd(e,2+(r?1:0),3),vd(e,Xc,Zc)):(sd(e,4+(r?1:0),3),function(e,t,n,r){var i;for(sd(e,t-257,5),sd(e,n-1,5),sd(e,r-4,4),i=0;i<r;i++)sd(e,e.bl_tree[2*Yc[i]+1],3);_d(e,e.dyn_ltree,t-1),_d(e,e.dyn_dtree,n-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,o+1),vd(e,e.dyn_ltree,e.dyn_dtree)),dd(e),r&&hd(e)},Tc._tr_tally=function(e,t,n){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&n,e.last_lit++,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(Kc[n]+Ac+1)]++,e.dyn_dtree[2*ad(t)]++),e.last_lit===e.lit_bufsize-1},Tc._tr_align=function(e){sd(e,2,3),ld(e,$c,Xc),function(e){16===e.bi_valid?(od(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)};var wd=function(e,t,n,r){for(var i=65535&e,a=e>>>16&65535,o=0;0!==n;){n-=o=n>2e3?2e3:n;do{a=a+(i=i+t[r++]|0)|0}while(--o);i%=65521,a%=65521}return i|a<<16};var xd=function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}();var Sd,kd=function(e,t,n,r){var i=xd,a=r+n;e^=-1;for(var o=r;o<a;o++)e=e>>>8^i[255&(e^t[o])];return~e},Td={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},Ed=xc,Cd=Tc,Od=wd,Md=kd,Ld=Td,Id=0,Ad=4,Bd=0,Nd=-2,Rd=-1,Pd=4,Dd=2,zd=8,Fd=9,$d=286,jd=30,Vd=19,Wd=2*$d+1,Hd=15,Ud=3,qd=258,Yd=qd+Ud+1,Xd=42,Zd=103,Gd=113,Kd=666,Jd=1,Qd=2,eh=3,th=4;function nh(e,t){return e.msg=Ld[t],t}function rh(e){return(e<<1)-(e>4?9:0)}function ih(e){for(var t=e.length;--t>=0;)e[t]=0}function ah(e){var t=e.state,n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(Ed.arraySet(e.output,t.pending_buf,t.pending_out,n,e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending&&(t.pending_out=0))}function oh(e,t){Cd._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,ah(e.strm)}function sh(e,t){e.pending_buf[e.pending++]=t}function lh(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function uh(e,t){var n,r,i=e.max_chain_length,a=e.strstart,o=e.prev_length,s=e.nice_match,l=e.strstart>e.w_size-Yd?e.strstart-(e.w_size-Yd):0,u=e.window,c=e.w_mask,d=e.prev,h=e.strstart+qd,f=u[a+o-1],p=u[a+o];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do{if(u[(n=t)+o]===p&&u[n+o-1]===f&&u[n]===u[a]&&u[++n]===u[a+1]){a+=2,n++;do{}while(u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&a<h);if(r=qd-(h-a),a=h-qd,r>o){if(e.match_start=t,o=r,r>=s)break;f=u[a+o-1],p=u[a+o]}}}while((t=d[t&c])>l&&0!=--i);return o<=e.lookahead?o:e.lookahead}function ch(e){var t,n,r,i,a,o,s,l,u,c,d=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=d+(d-Yd)){Ed.arraySet(e.window,e.window,d,d,0),e.match_start-=d,e.strstart-=d,e.block_start-=d,t=n=e.hash_size;do{r=e.head[--t],e.head[t]=r>=d?r-d:0}while(--n);t=n=d;do{r=e.prev[--t],e.prev[t]=r>=d?r-d:0}while(--n);i+=d}if(0===e.strm.avail_in)break;if(o=e.strm,s=e.window,l=e.strstart+e.lookahead,u=i,c=void 0,(c=o.avail_in)>u&&(c=u),n=0===c?0:(o.avail_in-=c,Ed.arraySet(s,o.input,o.next_in,c,l),1===o.state.wrap?o.adler=Od(o.adler,s,c,l):2===o.state.wrap&&(o.adler=Md(o.adler,s,c,l)),o.next_in+=c,o.total_in+=c,c),e.lookahead+=n,e.lookahead+e.insert>=Ud)for(a=e.strstart-e.insert,e.ins_h=e.window[a],e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+Ud-1])&e.hash_mask,e.prev[a&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=a,a++,e.insert--,!(e.lookahead+e.insert<Ud)););}while(e.lookahead<Yd&&0!==e.strm.avail_in)}function dh(e,t){for(var n,r;;){if(e.lookahead<Yd){if(ch(e),e.lookahead<Yd&&t===Id)return Jd;if(0===e.lookahead)break}if(n=0,e.lookahead>=Ud&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+Ud-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-Yd&&(e.match_length=uh(e,n)),e.match_length>=Ud)if(r=Cd._tr_tally(e,e.strstart-e.match_start,e.match_length-Ud),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=Ud){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+Ud-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else r=Cd._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(oh(e,!1),0===e.strm.avail_out))return Jd}return e.insert=e.strstart<Ud-1?e.strstart:Ud-1,t===Ad?(oh(e,!0),0===e.strm.avail_out?eh:th):e.last_lit&&(oh(e,!1),0===e.strm.avail_out)?Jd:Qd}function hh(e,t){for(var n,r,i;;){if(e.lookahead<Yd){if(ch(e),e.lookahead<Yd&&t===Id)return Jd;if(0===e.lookahead)break}if(n=0,e.lookahead>=Ud&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+Ud-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=Ud-1,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-Yd&&(e.match_length=uh(e,n),e.match_length<=5&&(1===e.strategy||e.match_length===Ud&&e.strstart-e.match_start>4096)&&(e.match_length=Ud-1)),e.prev_length>=Ud&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-Ud,r=Cd._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-Ud),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+Ud-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=Ud-1,e.strstart++,r&&(oh(e,!1),0===e.strm.avail_out))return Jd}else if(e.match_available){if((r=Cd._tr_tally(e,0,e.window[e.strstart-1]))&&oh(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return Jd}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=Cd._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<Ud-1?e.strstart:Ud-1,t===Ad?(oh(e,!0),0===e.strm.avail_out?eh:th):e.last_lit&&(oh(e,!1),0===e.strm.avail_out)?Jd:Qd}function fh(e,t,n,r,i){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=r,this.func=i}function ph(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=zd,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Ed.Buf16(2*Wd),this.dyn_dtree=new Ed.Buf16(2*(2*jd+1)),this.bl_tree=new Ed.Buf16(2*(2*Vd+1)),ih(this.dyn_ltree),ih(this.dyn_dtree),ih(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Ed.Buf16(Hd+1),this.heap=new Ed.Buf16(2*$d+1),ih(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Ed.Buf16(2*$d+1),ih(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function vh(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=Dd,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?Xd:Gd,e.adler=2===t.wrap?0:1,t.last_flush=Id,Cd._tr_init(t),Bd):nh(e,Nd)}function gh(e){var t,n=vh(e);return n===Bd&&((t=e.state).window_size=2*t.w_size,ih(t.head),t.max_lazy_match=Sd[t.level].max_lazy,t.good_match=Sd[t.level].good_length,t.nice_match=Sd[t.level].nice_length,t.max_chain_length=Sd[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=Ud-1,t.match_available=0,t.ins_h=0),n}function mh(e,t,n,r,i,a){if(!e)return Nd;var o=1;if(t===Rd&&(t=6),r<0?(o=0,r=-r):r>15&&(o=2,r-=16),i<1||i>Fd||n!==zd||r<8||r>15||t<0||t>9||a<0||a>Pd)return nh(e,Nd);8===r&&(r=9);var s=new ph;return e.state=s,s.strm=e,s.wrap=o,s.gzhead=null,s.w_bits=r,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+Ud-1)/Ud),s.window=new Ed.Buf8(2*s.w_size),s.head=new Ed.Buf16(s.hash_size),s.prev=new Ed.Buf16(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new Ed.Buf8(s.pending_buf_size),s.d_buf=1*s.lit_bufsize,s.l_buf=3*s.lit_bufsize,s.level=t,s.strategy=a,s.method=n,gh(e)}Sd=[new fh(0,0,0,0,(function(e,t){var n=65535;for(n>e.pending_buf_size-5&&(n=e.pending_buf_size-5);;){if(e.lookahead<=1){if(ch(e),0===e.lookahead&&t===Id)return Jd;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var r=e.block_start+n;if((0===e.strstart||e.strstart>=r)&&(e.lookahead=e.strstart-r,e.strstart=r,oh(e,!1),0===e.strm.avail_out))return Jd;if(e.strstart-e.block_start>=e.w_size-Yd&&(oh(e,!1),0===e.strm.avail_out))return Jd}return e.insert=0,t===Ad?(oh(e,!0),0===e.strm.avail_out?eh:th):(e.strstart>e.block_start&&(oh(e,!1),e.strm.avail_out),Jd)})),new fh(4,4,8,4,dh),new fh(4,5,16,8,dh),new fh(4,6,32,32,dh),new fh(4,4,16,16,hh),new fh(8,16,32,32,hh),new fh(8,16,128,128,hh),new fh(8,32,128,256,hh),new fh(32,128,258,1024,hh),new fh(32,258,258,4096,hh)],kc.deflateInit=function(e,t){return mh(e,t,zd,15,8,0)},kc.deflateInit2=mh,kc.deflateReset=gh,kc.deflateResetKeep=vh,kc.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?Nd:(e.state.gzhead=t,Bd):Nd},kc.deflate=function(e,t){var n,r,i,a;if(!e||!e.state||t>5||t<0)return e?nh(e,Nd):Nd;if(r=e.state,!e.output||!e.input&&0!==e.avail_in||r.status===Kd&&t!==Ad)return nh(e,0===e.avail_out?-5:Nd);if(r.strm=e,n=r.last_flush,r.last_flush=t,r.status===Xd)if(2===r.wrap)e.adler=0,sh(r,31),sh(r,139),sh(r,8),r.gzhead?(sh(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),sh(r,255&r.gzhead.time),sh(r,r.gzhead.time>>8&255),sh(r,r.gzhead.time>>16&255),sh(r,r.gzhead.time>>24&255),sh(r,9===r.level?2:r.strategy>=2||r.level<2?4:0),sh(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(sh(r,255&r.gzhead.extra.length),sh(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(e.adler=Md(e.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(sh(r,0),sh(r,0),sh(r,0),sh(r,0),sh(r,0),sh(r,9===r.level?2:r.strategy>=2||r.level<2?4:0),sh(r,3),r.status=Gd);else{var o=zd+(r.w_bits-8<<4)<<8;o|=(r.strategy>=2||r.level<2?0:r.level<6?1:6===r.level?2:3)<<6,0!==r.strstart&&(o|=32),o+=31-o%31,r.status=Gd,lh(r,o),0!==r.strstart&&(lh(r,e.adler>>>16),lh(r,65535&e.adler)),e.adler=1}if(69===r.status)if(r.gzhead.extra){for(i=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>i&&(e.adler=Md(e.adler,r.pending_buf,r.pending-i,i)),ah(e),i=r.pending,r.pending!==r.pending_buf_size));)sh(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>i&&(e.adler=Md(e.adler,r.pending_buf,r.pending-i,i)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(73===r.status)if(r.gzhead.name){i=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>i&&(e.adler=Md(e.adler,r.pending_buf,r.pending-i,i)),ah(e),i=r.pending,r.pending===r.pending_buf_size)){a=1;break}a=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0,sh(r,a)}while(0!==a);r.gzhead.hcrc&&r.pending>i&&(e.adler=Md(e.adler,r.pending_buf,r.pending-i,i)),0===a&&(r.gzindex=0,r.status=91)}else r.status=91;if(91===r.status)if(r.gzhead.comment){i=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>i&&(e.adler=Md(e.adler,r.pending_buf,r.pending-i,i)),ah(e),i=r.pending,r.pending===r.pending_buf_size)){a=1;break}a=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0,sh(r,a)}while(0!==a);r.gzhead.hcrc&&r.pending>i&&(e.adler=Md(e.adler,r.pending_buf,r.pending-i,i)),0===a&&(r.status=Zd)}else r.status=Zd;if(r.status===Zd&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&ah(e),r.pending+2<=r.pending_buf_size&&(sh(r,255&e.adler),sh(r,e.adler>>8&255),e.adler=0,r.status=Gd)):r.status=Gd),0!==r.pending){if(ah(e),0===e.avail_out)return r.last_flush=-1,Bd}else if(0===e.avail_in&&rh(t)<=rh(n)&&t!==Ad)return nh(e,-5);if(r.status===Kd&&0!==e.avail_in)return nh(e,-5);if(0!==e.avail_in||0!==r.lookahead||t!==Id&&r.status!==Kd){var s=2===r.strategy?function(e,t){for(var n;;){if(0===e.lookahead&&(ch(e),0===e.lookahead)){if(t===Id)return Jd;break}if(e.match_length=0,n=Cd._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(oh(e,!1),0===e.strm.avail_out))return Jd}return e.insert=0,t===Ad?(oh(e,!0),0===e.strm.avail_out?eh:th):e.last_lit&&(oh(e,!1),0===e.strm.avail_out)?Jd:Qd}(r,t):3===r.strategy?function(e,t){for(var n,r,i,a,o=e.window;;){if(e.lookahead<=qd){if(ch(e),e.lookahead<=qd&&t===Id)return Jd;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=Ud&&e.strstart>0&&(r=o[i=e.strstart-1])===o[++i]&&r===o[++i]&&r===o[++i]){a=e.strstart+qd;do{}while(r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&i<a);e.match_length=qd-(a-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=Ud?(n=Cd._tr_tally(e,1,e.match_length-Ud),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=Cd._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(oh(e,!1),0===e.strm.avail_out))return Jd}return e.insert=0,t===Ad?(oh(e,!0),0===e.strm.avail_out?eh:th):e.last_lit&&(oh(e,!1),0===e.strm.avail_out)?Jd:Qd}(r,t):Sd[r.level].func(r,t);if(s!==eh&&s!==th||(r.status=Kd),s===Jd||s===eh)return 0===e.avail_out&&(r.last_flush=-1),Bd;if(s===Qd&&(1===t?Cd._tr_align(r):5!==t&&(Cd._tr_stored_block(r,0,0,!1),3===t&&(ih(r.head),0===r.lookahead&&(r.strstart=0,r.block_start=0,r.insert=0))),ah(e),0===e.avail_out))return r.last_flush=-1,Bd}return t!==Ad?Bd:r.wrap<=0?1:(2===r.wrap?(sh(r,255&e.adler),sh(r,e.adler>>8&255),sh(r,e.adler>>16&255),sh(r,e.adler>>24&255),sh(r,255&e.total_in),sh(r,e.total_in>>8&255),sh(r,e.total_in>>16&255),sh(r,e.total_in>>24&255)):(lh(r,e.adler>>>16),lh(r,65535&e.adler)),ah(e),r.wrap>0&&(r.wrap=-r.wrap),0!==r.pending?Bd:1)},kc.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==Xd&&69!==t&&73!==t&&91!==t&&t!==Zd&&t!==Gd&&t!==Kd?nh(e,Nd):(e.state=null,t===Gd?nh(e,-3):Bd):Nd},kc.deflateSetDictionary=function(e,t){var n,r,i,a,o,s,l,u,c=t.length;if(!e||!e.state)return Nd;if(2===(a=(n=e.state).wrap)||1===a&&n.status!==Xd||n.lookahead)return Nd;for(1===a&&(e.adler=Od(e.adler,t,c,0)),n.wrap=0,c>=n.w_size&&(0===a&&(ih(n.head),n.strstart=0,n.block_start=0,n.insert=0),u=new Ed.Buf8(n.w_size),Ed.arraySet(u,t,c-n.w_size,n.w_size,0),t=u,c=n.w_size),o=e.avail_in,s=e.next_in,l=e.input,e.avail_in=c,e.next_in=0,e.input=t,ch(n);n.lookahead>=Ud;){r=n.strstart,i=n.lookahead-(Ud-1);do{n.ins_h=(n.ins_h<<n.hash_shift^n.window[r+Ud-1])&n.hash_mask,n.prev[r&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=r,r++}while(--i);n.strstart=r,n.lookahead=Ud-1,ch(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=Ud-1,n.match_available=0,e.next_in=s,e.input=l,e.avail_in=o,n.wrap=a,Bd},kc.deflateInfo="pako deflate (from Nodeca project)";var _h={},yh=xc,bh=!0,wh=!0;try{String.fromCharCode.apply(null,[0])}catch(fy){bh=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(fy){wh=!1}for(var xh=new yh.Buf8(256),Sh=0;Sh<256;Sh++)xh[Sh]=Sh>=252?6:Sh>=248?5:Sh>=240?4:Sh>=224?3:Sh>=192?2:1;function kh(e,t){if(t<65534&&(e.subarray&&wh||!e.subarray&&bh))return String.fromCharCode.apply(null,yh.shrinkBuf(e,t));for(var n="",r=0;r<t;r++)n+=String.fromCharCode(e[r]);return n}xh[254]=xh[254]=1,_h.string2buf=function(e){var t,n,r,i,a,o=e.length,s=0;for(i=0;i<o;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),s+=n<128?1:n<2048?2:n<65536?3:4;for(t=new yh.Buf8(s),a=0,i=0;a<s;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),n<128?t[a++]=n:n<2048?(t[a++]=192|n>>>6,t[a++]=128|63&n):n<65536?(t[a++]=224|n>>>12,t[a++]=128|n>>>6&63,t[a++]=128|63&n):(t[a++]=240|n>>>18,t[a++]=128|n>>>12&63,t[a++]=128|n>>>6&63,t[a++]=128|63&n);return t},_h.buf2binstring=function(e){return kh(e,e.length)},_h.binstring2buf=function(e){for(var t=new yh.Buf8(e.length),n=0,r=t.length;n<r;n++)t[n]=e.charCodeAt(n);return t},_h.buf2string=function(e,t){var n,r,i,a,o=t||e.length,s=new Array(2*o);for(r=0,n=0;n<o;)if((i=e[n++])<128)s[r++]=i;else if((a=xh[i])>4)s[r++]=65533,n+=a-1;else{for(i&=2===a?31:3===a?15:7;a>1&&n<o;)i=i<<6|63&e[n++],a--;a>1?s[r++]=65533:i<65536?s[r++]=i:(i-=65536,s[r++]=55296|i>>10&1023,s[r++]=56320|1023&i)}return kh(s,r)},_h.utf8border=function(e,t){var n;for((t=t||e.length)>e.length&&(t=e.length),n=t-1;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+xh[e[n]]>t?n:t};var Th=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},Eh=kc,Ch=xc,Oh=_h,Mh=Td,Lh=Th,Ih=Object.prototype.toString,Ah=0,Bh=-1,Nh=0,Rh=8;function Ph(e){if(!(this instanceof Ph))return new Ph(e);this.options=Ch.assign({level:Bh,method:Rh,chunkSize:16384,windowBits:15,memLevel:8,strategy:Nh,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Lh,this.strm.avail_out=0;var n=Eh.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(n!==Ah)throw new Error(Mh[n]);if(t.header&&Eh.deflateSetHeader(this.strm,t.header),t.dictionary){var r;if(r="string"==typeof t.dictionary?Oh.string2buf(t.dictionary):"[object ArrayBuffer]"===Ih.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(n=Eh.deflateSetDictionary(this.strm,r))!==Ah)throw new Error(Mh[n]);this._dict_set=!0}}function Dh(e,t){var n=new Ph(t);if(n.push(e,!0),n.err)throw n.msg||Mh[n.err];return n.result}Ph.prototype.push=function(e,t){var n,r,i=this.strm,a=this.options.chunkSize;if(this.ended)return!1;r=t===~~t?t:!0===t?4:0,"string"==typeof e?i.input=Oh.string2buf(e):"[object ArrayBuffer]"===Ih.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new Ch.Buf8(a),i.next_out=0,i.avail_out=a),1!==(n=Eh.deflate(i,r))&&n!==Ah)return this.onEnd(n),this.ended=!0,!1;0!==i.avail_out&&(0!==i.avail_in||4!==r&&2!==r)||("string"===this.options.to?this.onData(Oh.buf2binstring(Ch.shrinkBuf(i.output,i.next_out))):this.onData(Ch.shrinkBuf(i.output,i.next_out)))}while((i.avail_in>0||0===i.avail_out)&&1!==n);return 4===r?(n=Eh.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===Ah):2!==r||(this.onEnd(Ah),i.avail_out=0,!0)},Ph.prototype.onData=function(e){this.chunks.push(e)},Ph.prototype.onEnd=function(e){e===Ah&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=Ch.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},Sc.Deflate=Ph,Sc.deflate=Dh,Sc.deflateRaw=function(e,t){return(t=t||{}).raw=!0,Dh(e,t)},Sc.gzip=function(e,t){return(t=t||{}).gzip=!0,Dh(e,t)};var zh={},Fh={},$h=xc,jh=15,Vh=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],Wh=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],Hh=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],Uh=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64],qh=xc,Yh=wd,Xh=kd,Zh=function(e,t){var n,r,i,a,o,s,l,u,c,d,h,f,p,v,g,m,_,y,b,w,x,S,k,T,E;n=e.state,r=e.next_in,T=e.input,i=r+(e.avail_in-5),a=e.next_out,E=e.output,o=a-(t-e.avail_out),s=a+(e.avail_out-257),l=n.dmax,u=n.wsize,c=n.whave,d=n.wnext,h=n.window,f=n.hold,p=n.bits,v=n.lencode,g=n.distcode,m=(1<<n.lenbits)-1,_=(1<<n.distbits)-1;e:do{p<15&&(f+=T[r++]<<p,p+=8,f+=T[r++]<<p,p+=8),y=v[f&m];t:for(;;){if(f>>>=b=y>>>24,p-=b,0===(b=y>>>16&255))E[a++]=65535&y;else{if(!(16&b)){if(64&b){if(32&b){n.mode=12;break e}e.msg="invalid literal/length code",n.mode=30;break e}y=v[(65535&y)+(f&(1<<b)-1)];continue t}for(w=65535&y,(b&=15)&&(p<b&&(f+=T[r++]<<p,p+=8),w+=f&(1<<b)-1,f>>>=b,p-=b),p<15&&(f+=T[r++]<<p,p+=8,f+=T[r++]<<p,p+=8),y=g[f&_];;){if(f>>>=b=y>>>24,p-=b,16&(b=y>>>16&255)){if(x=65535&y,p<(b&=15)&&(f+=T[r++]<<p,(p+=8)<b&&(f+=T[r++]<<p,p+=8)),(x+=f&(1<<b)-1)>l){e.msg="invalid distance too far back",n.mode=30;break e}if(f>>>=b,p-=b,x>(b=a-o)){if((b=x-b)>c&&n.sane){e.msg="invalid distance too far back",n.mode=30;break e}if(S=0,k=h,0===d){if(S+=u-b,b<w){w-=b;do{E[a++]=h[S++]}while(--b);S=a-x,k=E}}else if(d<b){if(S+=u+d-b,(b-=d)<w){w-=b;do{E[a++]=h[S++]}while(--b);if(S=0,d<w){w-=b=d;do{E[a++]=h[S++]}while(--b);S=a-x,k=E}}}else if(S+=d-b,b<w){w-=b;do{E[a++]=h[S++]}while(--b);S=a-x,k=E}for(;w>2;)E[a++]=k[S++],E[a++]=k[S++],E[a++]=k[S++],w-=3;w&&(E[a++]=k[S++],w>1&&(E[a++]=k[S++]))}else{S=a-x;do{E[a++]=E[S++],E[a++]=E[S++],E[a++]=E[S++],w-=3}while(w>2);w&&(E[a++]=E[S++],w>1&&(E[a++]=E[S++]))}break}if(64&b){e.msg="invalid distance code",n.mode=30;break e}y=g[(65535&y)+(f&(1<<b)-1)]}}break}}while(r<i&&a<s);r-=w=p>>3,f&=(1<<(p-=w<<3))-1,e.next_in=r,e.next_out=a,e.avail_in=r<i?i-r+5:5-(r-i),e.avail_out=a<s?s-a+257:257-(a-s),n.hold=f,n.bits=p},Gh=function(e,t,n,r,i,a,o,s){var l,u,c,d,h,f,p,v,g,m=s.bits,_=0,y=0,b=0,w=0,x=0,S=0,k=0,T=0,E=0,C=0,O=null,M=0,L=new $h.Buf16(16),I=new $h.Buf16(16),A=null,B=0;for(_=0;_<=jh;_++)L[_]=0;for(y=0;y<r;y++)L[t[n+y]]++;for(x=m,w=jh;w>=1&&0===L[w];w--);if(x>w&&(x=w),0===w)return i[a++]=20971520,i[a++]=20971520,s.bits=1,0;for(b=1;b<w&&0===L[b];b++);for(x<b&&(x=b),T=1,_=1;_<=jh;_++)if(T<<=1,(T-=L[_])<0)return-1;if(T>0&&(0===e||1!==w))return-1;for(I[1]=0,_=1;_<jh;_++)I[_+1]=I[_]+L[_];for(y=0;y<r;y++)0!==t[n+y]&&(o[I[t[n+y]]++]=y);if(0===e?(O=A=o,f=19):1===e?(O=Vh,M-=257,A=Wh,B-=257,f=256):(O=Hh,A=Uh,f=-1),C=0,y=0,_=b,h=a,S=x,k=0,c=-1,d=(E=1<<x)-1,1===e&&E>852||2===e&&E>592)return 1;for(;;){p=_-k,o[y]<f?(v=0,g=o[y]):o[y]>f?(v=A[B+o[y]],g=O[M+o[y]]):(v=96,g=0),l=1<<_-k,b=u=1<<S;do{i[h+(C>>k)+(u-=l)]=p<<24|v<<16|g}while(0!==u);for(l=1<<_-1;C&l;)l>>=1;if(0!==l?(C&=l-1,C+=l):C=0,y++,0==--L[_]){if(_===w)break;_=t[n+o[y]]}if(_>x&&(C&d)!==c){for(0===k&&(k=x),h+=b,T=1<<(S=_-k);S+k<w&&!((T-=L[S+k])<=0);)S++,T<<=1;if(E+=1<<S,1===e&&E>852||2===e&&E>592)return 1;i[c=C&d]=x<<24|S<<16|h-a}}return 0!==C&&(i[h+C]=_-k<<24|64<<16),s.bits=x,0},Kh=1,Jh=2,Qh=0,ef=-2,tf=1,nf=12,rf=30,af=852,of=592;function sf(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function lf(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new qh.Buf16(320),this.work=new qh.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function uf(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=tf,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new qh.Buf32(af),t.distcode=t.distdyn=new qh.Buf32(of),t.sane=1,t.back=-1,Qh):ef}function cf(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,uf(e)):ef}function df(e,t){var n,r;return e&&e.state?(r=e.state,t<0?(n=0,t=-t):(n=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?ef:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=n,r.wbits=t,cf(e))):ef}function hf(e,t){var n,r;return e?(r=new lf,e.state=r,r.window=null,(n=df(e,t))!==Qh&&(e.state=null),n):ef}var ff,pf,vf=!0;function gf(e){if(vf){var t;for(ff=new qh.Buf32(512),pf=new qh.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(Gh(Kh,e.lens,0,288,ff,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;Gh(Jh,e.lens,0,32,pf,0,e.work,{bits:5}),vf=!1}e.lencode=ff,e.lenbits=9,e.distcode=pf,e.distbits=5}function mf(e,t,n,r){var i,a=e.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new qh.Buf8(a.wsize)),r>=a.wsize?(qh.arraySet(a.window,t,n-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((i=a.wsize-a.wnext)>r&&(i=r),qh.arraySet(a.window,t,n-r,i,a.wnext),(r-=i)?(qh.arraySet(a.window,t,n-r,r,0),a.wnext=r,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0}Fh.inflateReset=cf,Fh.inflateReset2=df,Fh.inflateResetKeep=uf,Fh.inflateInit=function(e){return hf(e,15)},Fh.inflateInit2=hf,Fh.inflate=function(e,t){var n,r,i,a,o,s,l,u,c,d,h,f,p,v,g,m,_,y,b,w,x,S,k,T,E=0,C=new qh.Buf8(4),O=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return ef;(n=e.state).mode===nf&&(n.mode=13),o=e.next_out,i=e.output,l=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,u=n.hold,c=n.bits,d=s,h=l,S=Qh;e:for(;;)switch(n.mode){case tf:if(0===n.wrap){n.mode=13;break}for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(2&n.wrap&&35615===u){n.check=0,C[0]=255&u,C[1]=u>>>8&255,n.check=Xh(n.check,C,2,0),u=0,c=0,n.mode=2;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&u)<<8)+(u>>8))%31){e.msg="incorrect header check",n.mode=rf;break}if(8!=(15&u)){e.msg="unknown compression method",n.mode=rf;break}if(c-=4,x=8+(15&(u>>>=4)),0===n.wbits)n.wbits=x;else if(x>n.wbits){e.msg="invalid window size",n.mode=rf;break}n.dmax=1<<x,e.adler=n.check=1,n.mode=512&u?10:nf,u=0,c=0;break;case 2:for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(n.flags=u,8!=(255&n.flags)){e.msg="unknown compression method",n.mode=rf;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=rf;break}n.head&&(n.head.text=u>>8&1),512&n.flags&&(C[0]=255&u,C[1]=u>>>8&255,n.check=Xh(n.check,C,2,0)),u=0,c=0,n.mode=3;case 3:for(;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.head&&(n.head.time=u),512&n.flags&&(C[0]=255&u,C[1]=u>>>8&255,C[2]=u>>>16&255,C[3]=u>>>24&255,n.check=Xh(n.check,C,4,0)),u=0,c=0,n.mode=4;case 4:for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.head&&(n.head.xflags=255&u,n.head.os=u>>8),512&n.flags&&(C[0]=255&u,C[1]=u>>>8&255,n.check=Xh(n.check,C,2,0)),u=0,c=0,n.mode=5;case 5:if(1024&n.flags){for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.length=u,n.head&&(n.head.extra_len=u),512&n.flags&&(C[0]=255&u,C[1]=u>>>8&255,n.check=Xh(n.check,C,2,0)),u=0,c=0}else n.head&&(n.head.extra=null);n.mode=6;case 6:if(1024&n.flags&&((f=n.length)>s&&(f=s),f&&(n.head&&(x=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Array(n.head.extra_len)),qh.arraySet(n.head.extra,r,a,f,x)),512&n.flags&&(n.check=Xh(n.check,r,f,a)),s-=f,a+=f,n.length-=f),n.length))break e;n.length=0,n.mode=7;case 7:if(2048&n.flags){if(0===s)break e;f=0;do{x=r[a+f++],n.head&&x&&n.length<65536&&(n.head.name+=String.fromCharCode(x))}while(x&&f<s);if(512&n.flags&&(n.check=Xh(n.check,r,f,a)),s-=f,a+=f,x)break e}else n.head&&(n.head.name=null);n.length=0,n.mode=8;case 8:if(4096&n.flags){if(0===s)break e;f=0;do{x=r[a+f++],n.head&&x&&n.length<65536&&(n.head.comment+=String.fromCharCode(x))}while(x&&f<s);if(512&n.flags&&(n.check=Xh(n.check,r,f,a)),s-=f,a+=f,x)break e}else n.head&&(n.head.comment=null);n.mode=9;case 9:if(512&n.flags){for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(u!==(65535&n.check)){e.msg="header crc mismatch",n.mode=rf;break}u=0,c=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=nf;break;case 10:for(;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}e.adler=n.check=sf(u),u=0,c=0,n.mode=11;case 11:if(0===n.havedict)return e.next_out=o,e.avail_out=l,e.next_in=a,e.avail_in=s,n.hold=u,n.bits=c,2;e.adler=n.check=1,n.mode=nf;case nf:if(5===t||6===t)break e;case 13:if(n.last){u>>>=7&c,c-=7&c,n.mode=27;break}for(;c<3;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}switch(n.last=1&u,c-=1,3&(u>>>=1)){case 0:n.mode=14;break;case 1:if(gf(n),n.mode=20,6===t){u>>>=2,c-=2;break e}break;case 2:n.mode=17;break;case 3:e.msg="invalid block type",n.mode=rf}u>>>=2,c-=2;break;case 14:for(u>>>=7&c,c-=7&c;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if((65535&u)!=(u>>>16^65535)){e.msg="invalid stored block lengths",n.mode=rf;break}if(n.length=65535&u,u=0,c=0,n.mode=15,6===t)break e;case 15:n.mode=16;case 16:if(f=n.length){if(f>s&&(f=s),f>l&&(f=l),0===f)break e;qh.arraySet(i,r,a,f,o),s-=f,a+=f,l-=f,o+=f,n.length-=f;break}n.mode=nf;break;case 17:for(;c<14;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(n.nlen=257+(31&u),u>>>=5,c-=5,n.ndist=1+(31&u),u>>>=5,c-=5,n.ncode=4+(15&u),u>>>=4,c-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=rf;break}n.have=0,n.mode=18;case 18:for(;n.have<n.ncode;){for(;c<3;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.lens[O[n.have++]]=7&u,u>>>=3,c-=3}for(;n.have<19;)n.lens[O[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,k={bits:n.lenbits},S=Gh(0,n.lens,0,19,n.lencode,0,n.work,k),n.lenbits=k.bits,S){e.msg="invalid code lengths set",n.mode=rf;break}n.have=0,n.mode=19;case 19:for(;n.have<n.nlen+n.ndist;){for(;m=(E=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,_=65535&E,!((g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(_<16)u>>>=g,c-=g,n.lens[n.have++]=_;else{if(16===_){for(T=g+2;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(u>>>=g,c-=g,0===n.have){e.msg="invalid bit length repeat",n.mode=rf;break}x=n.lens[n.have-1],f=3+(3&u),u>>>=2,c-=2}else if(17===_){for(T=g+3;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}c-=g,x=0,f=3+(7&(u>>>=g)),u>>>=3,c-=3}else{for(T=g+7;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}c-=g,x=0,f=11+(127&(u>>>=g)),u>>>=7,c-=7}if(n.have+f>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=rf;break}for(;f--;)n.lens[n.have++]=x}}if(n.mode===rf)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=rf;break}if(n.lenbits=9,k={bits:n.lenbits},S=Gh(Kh,n.lens,0,n.nlen,n.lencode,0,n.work,k),n.lenbits=k.bits,S){e.msg="invalid literal/lengths set",n.mode=rf;break}if(n.distbits=6,n.distcode=n.distdyn,k={bits:n.distbits},S=Gh(Jh,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,k),n.distbits=k.bits,S){e.msg="invalid distances set",n.mode=rf;break}if(n.mode=20,6===t)break e;case 20:n.mode=21;case 21:if(s>=6&&l>=258){e.next_out=o,e.avail_out=l,e.next_in=a,e.avail_in=s,n.hold=u,n.bits=c,Zh(e,h),o=e.next_out,i=e.output,l=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,u=n.hold,c=n.bits,n.mode===nf&&(n.back=-1);break}for(n.back=0;m=(E=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,_=65535&E,!((g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(m&&!(240&m)){for(y=g,b=m,w=_;m=(E=n.lencode[w+((u&(1<<y+b)-1)>>y)])>>>16&255,_=65535&E,!(y+(g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}u>>>=y,c-=y,n.back+=y}if(u>>>=g,c-=g,n.back+=g,n.length=_,0===m){n.mode=26;break}if(32&m){n.back=-1,n.mode=nf;break}if(64&m){e.msg="invalid literal/length code",n.mode=rf;break}n.extra=15&m,n.mode=22;case 22:if(n.extra){for(T=n.extra;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.length+=u&(1<<n.extra)-1,u>>>=n.extra,c-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=23;case 23:for(;m=(E=n.distcode[u&(1<<n.distbits)-1])>>>16&255,_=65535&E,!((g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(!(240&m)){for(y=g,b=m,w=_;m=(E=n.distcode[w+((u&(1<<y+b)-1)>>y)])>>>16&255,_=65535&E,!(y+(g=E>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}u>>>=y,c-=y,n.back+=y}if(u>>>=g,c-=g,n.back+=g,64&m){e.msg="invalid distance code",n.mode=rf;break}n.offset=_,n.extra=15&m,n.mode=24;case 24:if(n.extra){for(T=n.extra;c<T;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.offset+=u&(1<<n.extra)-1,u>>>=n.extra,c-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=rf;break}n.mode=25;case 25:if(0===l)break e;if(f=h-l,n.offset>f){if((f=n.offset-f)>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=rf;break}f>n.wnext?(f-=n.wnext,p=n.wsize-f):p=n.wnext-f,f>n.length&&(f=n.length),v=n.window}else v=i,p=o-n.offset,f=n.length;f>l&&(f=l),l-=f,n.length-=f;do{i[o++]=v[p++]}while(--f);0===n.length&&(n.mode=21);break;case 26:if(0===l)break e;i[o++]=n.length,l--,n.mode=21;break;case 27:if(n.wrap){for(;c<32;){if(0===s)break e;s--,u|=r[a++]<<c,c+=8}if(h-=l,e.total_out+=h,n.total+=h,h&&(e.adler=n.check=n.flags?Xh(n.check,i,h,o-h):Yh(n.check,i,h,o-h)),h=l,(n.flags?u:sf(u))!==n.check){e.msg="incorrect data check",n.mode=rf;break}u=0,c=0}n.mode=28;case 28:if(n.wrap&&n.flags){for(;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(u!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=rf;break}u=0,c=0}n.mode=29;case 29:S=1;break e;case rf:S=-3;break e;case 31:return-4;default:return ef}return e.next_out=o,e.avail_out=l,e.next_in=a,e.avail_in=s,n.hold=u,n.bits=c,(n.wsize||h!==e.avail_out&&n.mode<rf&&(n.mode<27||4!==t))&&mf(e,e.output,e.next_out,h-e.avail_out),d-=e.avail_in,h-=e.avail_out,e.total_in+=d,e.total_out+=h,n.total+=h,n.wrap&&h&&(e.adler=n.check=n.flags?Xh(n.check,i,h,e.next_out-h):Yh(n.check,i,h,e.next_out-h)),e.data_type=n.bits+(n.last?64:0)+(n.mode===nf?128:0)+(20===n.mode||15===n.mode?256:0),(0===d&&0===h||4===t)&&S===Qh&&(S=-5),S},Fh.inflateEnd=function(e){if(!e||!e.state)return ef;var t=e.state;return t.window&&(t.window=null),e.state=null,Qh},Fh.inflateGetHeader=function(e,t){var n;return e&&e.state&&2&(n=e.state).wrap?(n.head=t,t.done=!1,Qh):ef},Fh.inflateSetDictionary=function(e,t){var n,r=t.length;return e&&e.state?0!==(n=e.state).wrap&&11!==n.mode?ef:11===n.mode&&Yh(1,t,r,0)!==n.check?-3:mf(e,t,r,r)?(n.mode=31,-4):(n.havedict=1,Qh):ef},Fh.inflateInfo="pako inflate (from Nodeca project)";var _f={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};var yf=Fh,bf=xc,wf=_h,xf=_f,Sf=Td,kf=Th,Tf=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1},Ef=Object.prototype.toString;function Cf(e){if(!(this instanceof Cf))return new Cf(e);this.options=bf.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&(15&t.windowBits||(t.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new kf,this.strm.avail_out=0;var n=yf.inflateInit2(this.strm,t.windowBits);if(n!==xf.Z_OK)throw new Error(Sf[n]);if(this.header=new Tf,yf.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=wf.string2buf(t.dictionary):"[object ArrayBuffer]"===Ef.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(n=yf.inflateSetDictionary(this.strm,t.dictionary))!==xf.Z_OK))throw new Error(Sf[n])}function Of(e,t){var n=new Cf(t);if(n.push(e,!0),n.err)throw n.msg||Sf[n.err];return n.result}Cf.prototype.push=function(e,t){var n,r,i,a,o,s=this.strm,l=this.options.chunkSize,u=this.options.dictionary,c=!1;if(this.ended)return!1;r=t===~~t?t:!0===t?xf.Z_FINISH:xf.Z_NO_FLUSH,"string"==typeof e?s.input=wf.binstring2buf(e):"[object ArrayBuffer]"===Ef.call(e)?s.input=new Uint8Array(e):s.input=e,s.next_in=0,s.avail_in=s.input.length;do{if(0===s.avail_out&&(s.output=new bf.Buf8(l),s.next_out=0,s.avail_out=l),(n=yf.inflate(s,xf.Z_NO_FLUSH))===xf.Z_NEED_DICT&&u&&(n=yf.inflateSetDictionary(this.strm,u)),n===xf.Z_BUF_ERROR&&!0===c&&(n=xf.Z_OK,c=!1),n!==xf.Z_STREAM_END&&n!==xf.Z_OK)return this.onEnd(n),this.ended=!0,!1;s.next_out&&(0!==s.avail_out&&n!==xf.Z_STREAM_END&&(0!==s.avail_in||r!==xf.Z_FINISH&&r!==xf.Z_SYNC_FLUSH)||("string"===this.options.to?(i=wf.utf8border(s.output,s.next_out),a=s.next_out-i,o=wf.buf2string(s.output,i),s.next_out=a,s.avail_out=l-a,a&&bf.arraySet(s.output,s.output,i,a,0),this.onData(o)):this.onData(bf.shrinkBuf(s.output,s.next_out)))),0===s.avail_in&&0===s.avail_out&&(c=!0)}while((s.avail_in>0||0===s.avail_out)&&n!==xf.Z_STREAM_END);return n===xf.Z_STREAM_END&&(r=xf.Z_FINISH),r===xf.Z_FINISH?(n=yf.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===xf.Z_OK):r!==xf.Z_SYNC_FLUSH||(this.onEnd(xf.Z_OK),s.avail_out=0,!0)},Cf.prototype.onData=function(e){this.chunks.push(e)},Cf.prototype.onEnd=function(e){e===xf.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=bf.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},zh.Inflate=Cf,zh.inflate=Of,zh.inflateRaw=function(e,t){return(t=t||{}).raw=!0,Of(e,t)},zh.ungzip=Of;var Mf={};(0,xc.assign)(Mf,Sc,zh,_f);var Lf=Mf,If=!1,Af=0,Bf=0,Nf=960,Rf=375,Pf=750;function Df(e,t){var n=Number(e);return isNaN(n)?t:n}var zf=vc(0,((e,t)=>{var n;if(0===Af&&(!function(){var{platform:e,pixelRatio:t,windowWidth:n}=gc();Af=n,Bf=t,If="ios"===e}(),n=__uniConfig.globalStyle||{},Nf=Df(n.rpxCalcMaxDeviceWidth,960),Rf=Df(n.rpxCalcBaseDeviceWidth,375),Pf=Df(n.rpxCalcBaseDeviceWidth,750)),0===(e=Number(e)))return 0;var r=t||Af,i=e/750*(r=e===Pf||r<=Nf?r:Rf);return i<0&&(i=-i),0===(i=Math.floor(i+1e-4))&&(i=1!==Bf&&If?.5:1),e<0?-i:i})),Ff={};Ff.f={}.propertyIsEnumerable;var $f,jf=M,Vf=Ve,Wf=Z,Hf=Ff.f,Uf=($f=!1,function(e){for(var t,n=Wf(e),r=Vf(n),i=r.length,a=0,o=[];i>a;)t=r[a++],jf&&!Hf.call(n,t)||o.push($f?[t,n[t]]:n[t]);return o});be(be.S,"Object",{values:function(e){return Uf(e)}});var qf="setPageMeta",Yf="loadFontFace",Xf="pageScrollTo",Zf=function(){if("object"==typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var e=function(e){for(var t=window.document,n=i(t);n;)n=i(t=n.ownerDocument);return t}(),t=[],n=null,r=null;o.prototype.THROTTLE_TIMEOUT=100,o.prototype.POLL_INTERVAL=null,o.prototype.USE_MUTATION_OBSERVER=!0,o._setupCrossOriginUpdater=function(){return n||(n=function(e,n){r=e&&n?d(e,n):{top:0,bottom:0,left:0,right:0,width:0,height:0},t.forEach((function(e){e._checkForIntersections()}))}),n},o._resetCrossOriginUpdater=function(){n=null,r=null},o.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},o.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},o.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},o.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},o.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},o.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},o.prototype._monitorIntersections=function(t){var n=t.defaultView;if(n&&-1==this._monitoringDocuments.indexOf(t)){var r=this._checkForIntersections,a=null,o=null;this.POLL_INTERVAL?a=n.setInterval(r,this.POLL_INTERVAL):(s(n,"resize",r,!0),s(t,"scroll",r,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in n&&(o=new n.MutationObserver(r)).observe(t,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(t),this._monitoringUnsubscribes.push((function(){var e=t.defaultView;e&&(a&&e.clearInterval(a),l(e,"resize",r,!0)),l(t,"scroll",r,!0),o&&o.disconnect()}));var u=this.root&&(this.root.ownerDocument||this.root)||e;if(t!=u){var c=i(t);c&&this._monitorIntersections(c.ownerDocument)}}},o.prototype._unmonitorIntersections=function(t){var n=this._monitoringDocuments.indexOf(t);if(-1!=n){var r=this.root&&(this.root.ownerDocument||this.root)||e;if(!this._observationTargets.some((function(e){var n=e.element.ownerDocument;if(n==t)return!0;for(;n&&n!=r;){var a=i(n);if((n=a&&a.ownerDocument)==t)return!0}return!1}))){var a=this._monitoringUnsubscribes[n];if(this._monitoringDocuments.splice(n,1),this._monitoringUnsubscribes.splice(n,1),a(),t!=r){var o=i(t);o&&this._unmonitorIntersections(o.ownerDocument)}}}},o.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},o.prototype._checkForIntersections=function(){if(this.root||!n||r){var e=this._rootIsInDom(),t=e?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach((function(r){var i=r.element,o=u(i),s=this._rootContainsTarget(i),l=r.entry,c=e&&s&&this._computeTargetAndRootIntersection(i,o,t),d=null;this._rootContainsTarget(i)?n&&!this.root||(d=t):d={top:0,bottom:0,left:0,right:0,width:0,height:0};var h=r.entry=new a({time:window.performance&&performance.now&&performance.now(),target:i,boundingClientRect:o,rootBounds:d,intersectionRect:c});l?e&&s?this._hasCrossedThreshold(l,h)&&this._queuedEntries.push(h):l&&l.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},o.prototype._computeTargetAndRootIntersection=function(t,i,a){if("none"!=window.getComputedStyle(t).display){for(var o,s,l,c,h,p,v,g,m=i,_=f(t),y=!1;!y&&_;){var b=null,w=1==_.nodeType?window.getComputedStyle(_):{};if("none"==w.display)return null;if(_==this.root||9==_.nodeType)if(y=!0,_==this.root||_==e)n&&!this.root?!r||0==r.width&&0==r.height?(_=null,b=null,m=null):b=r:b=a;else{var x=f(_),S=x&&u(x),k=x&&this._computeTargetAndRootIntersection(x,S,a);S&&k?(_=x,b=d(S,k)):(_=null,m=null)}else{var T=_.ownerDocument;_!=T.body&&_!=T.documentElement&&"visible"!=w.overflow&&(b=u(_))}if(b&&(o=b,s=m,l=void 0,c=void 0,h=void 0,p=void 0,v=void 0,g=void 0,l=Math.max(o.top,s.top),c=Math.min(o.bottom,s.bottom),h=Math.max(o.left,s.left),p=Math.min(o.right,s.right),g=c-l,m=(v=p-h)>=0&&g>=0&&{top:l,bottom:c,left:h,right:p,width:v,height:g}||null),!m)break;_=_&&f(_)}return m}},o.prototype._getRootRect=function(){var t;if(this.root&&!p(this.root))t=u(this.root);else{var n=p(this.root)?this.root:e,r=n.documentElement,i=n.body;t={top:0,left:0,right:r.clientWidth||i.clientWidth,width:r.clientWidth||i.clientWidth,bottom:r.clientHeight||i.clientHeight,height:r.clientHeight||i.clientHeight}}return this._expandRectByRootMargin(t)},o.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},o.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,r=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==r)for(var i=0;i<this.thresholds.length;i++){var a=this.thresholds[i];if(a==n||a==r||a<n!=a<r)return!0}},o.prototype._rootIsInDom=function(){return!this.root||h(e,this.root)},o.prototype._rootContainsTarget=function(t){var n=this.root&&(this.root.ownerDocument||this.root)||e;return h(n,t)&&(!this.root||n==t.ownerDocument)},o.prototype._registerInstance=function(){t.indexOf(this)<0&&t.push(this)},o.prototype._unregisterInstance=function(){var e=t.indexOf(this);-1!=e&&t.splice(e,1)},window.IntersectionObserver=o,window.IntersectionObserverEntry=a}function i(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(t){return null}}function a(e){this.time=e.time,this.target=e.target,this.rootBounds=c(e.rootBounds),this.boundingClientRect=c(e.boundingClientRect),this.intersectionRect=c(e.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,r=this.intersectionRect,i=r.width*r.height;this.intersectionRatio=n?Number((i/n).toFixed(4)):this.isIntersecting?1:0}function o(e,t){var n=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(n.root&&1!=n.root.nodeType&&9!=n.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=function(e,t){var n=null;return function(){n||(n=setTimeout((function(){e(),n=null}),t))}}(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function s(e,t,n,r){"function"==typeof e.addEventListener?e.addEventListener(t,n,r||!1):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function l(e,t,n,r){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,r||!1):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function u(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function c(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function d(e,t){var n=t.top-e.top,r=t.left-e.left;return{top:n,left:r,height:t.height,width:t.width,bottom:n+t.height,right:r+t.width}}function h(e,t){for(var n=t;n;){if(n==e)return!0;n=f(n)}return!1}function f(t){var n=t.parentNode;return 9==t.nodeType&&t!=e?i(t):(n&&n.assignedSlot&&(n=n.assignedSlot.parentNode),n&&11==n.nodeType&&n.host?n.host:n)}function p(e){return e&&9===e.nodeType}};function Gf(e){var{bottom:t,height:n,left:r,right:i,top:a,width:o}=e||{};return{bottom:t,height:n,left:r,right:i,top:a,width:o}}function Kf(e){var{intersectionRatio:t,boundingClientRect:{height:n,width:r},intersectionRect:{height:i,width:a}}=e;return 0!==t?t:i===n?a/r:i/n}const Jf=Object.freeze(Object.defineProperty({__proto__:null,navigateBack:function(e){UniViewJSBridge.invokeServiceMethod("navigateBack",e)},navigateTo:function(e){UniViewJSBridge.invokeServiceMethod("navigateTo",e)},reLaunch:function(e){UniViewJSBridge.invokeServiceMethod("reLaunch",e)},redirectTo:function(e){UniViewJSBridge.invokeServiceMethod("redirectTo",e)},switchTab:function(e){UniViewJSBridge.invokeServiceMethod("switchTab",e)},upx2px:zf},Symbol.toStringTag,{value:"Module"}));function Qf(e,t){if(t)return un(t,"a")&&(t.a=e(t.a)),un(t,"e")&&(t.e=e(t.e,!1)),un(t,"w")&&(t.w=function(e,t){var n={};return e.forEach((e=>{var[r,[i,a]]=e;n[t(r)]=[t(i),a]})),n}(t.w,e)),un(t,"s")&&(t.s=e(t.s)),un(t,"t")&&(t.t=e(t.t)),t}var ep=new Map;function tp(e){return ep.get(e)}function np(e){return ep.delete(e)}var rp=new Set;function ip(e,t){rp.add(function(e,t){return e.priority=t,e}(e,t))}function ap(e,t){var n=window["__"+ar],r=n&&n[e];return r||(t&&t.__renderjsInstances?t.__renderjsInstances[e]:void 0)}var op=6;function sp(e,t,n){var[r,i,a,o]=up(t),s=lp(e,r);if(cn(n)||cn(o)){var[l,u]=a.split(".");return cp(s,i,l,u,n||o)}return function(e,t,n){var r=ap(t,e);if(!r)return console.error(dr("wxs","module "+n+" not found"));return pr(r,n.slice(n.indexOf(".")+1))}(s,i,a)}function lp(e,t){if(e.__ownerId===t)return e;for(var n=e.parentElement;n;){if(n.__ownerId===t)return n;n=n.parentElement}return e}function up(e){return JSON.parse(e.slice(op))}function cp(e,t,n,r,i){var a=ap(t,e);if(!a)return console.error(dr("wxs","module "+n+" not found"));var o=a[r];return fn(o)?o.apply(a,i):console.error(n+"."+r+" is not a function")}function dp(e,t,n){var r=n;return n=>{try{!function(e,t,n,r){var[i,a,o]=up(e),s=lp(t,i),[l,u]=o.split(".");cp(s,a,l,u,[n,r,Ju(ec(s)),Ju(ec(t))])}(t,e.$,n,r)}catch(i){console.error(i)}r=n}}function hp(e,t){var n=ec(t);return Object.defineProperty(e,"instance",{get:()=>Ju(n)}),e}function fp(e,t){Object.keys(t).forEach((n=>{!function(e,t){var n=function(e){var t=window["__"+or],n=t&&t[e];if(!n)return console.error(dr("renderjs",e+" not found"));return n}(t);if(!n)return;var r=e.$;(r.__renderjsInstances||(r.__renderjsInstances={}))[t]=function(e,t){return t=t.default||t,t.render=()=>{},fu(t).mixin({mounted(){this.$ownerInstance=Ju(ec(e))}}).mount(document.createElement("div"))}(r,n)}(e,t[n])}))}function pp(e,t){return pn(e)?(0===e.indexOf(ir)?e=JSON.parse(e.slice(7)):0===e.indexOf(rr)&&(e=sp(t,e)),e):e}function vp(e){return 0===e.indexOf("--")}class gp{constructor(e,t,n,r){this.isMounted=!1,this.isUnmounted=!1,this.$hasWxsProps=!1,this.$children=[],this.id=e,this.tag=t,this.pid=n,r&&(this.$=r),this.$wxsProps=new Map;var i=this.$parent=function(e){return ep.get(e)}(n);i&&i.appendUniChild(this)}init(e){un(e,"t")&&(this.$.textContent=e.t)}setText(e){this.$.textContent=e,this.updateView()}insert(e,t,n){n&&this.init(n,!1);var r=this.$,i=tp(e);-1===t?i.appendChild(r):i.insertBefore(r,tp(t).$),this.isMounted=!0}remove(){this.removeUniParent();var{$:e}=this;e.parentNode.removeChild(e),this.isUnmounted=!0,np(this.id),function(e){var{__renderjsInstances:t}=e.$;t&&Object.keys(t).forEach((e=>{t[e].$.appContext.app.unmount()}))}(this),this.removeUniChildren(),this.updateView()}appendChild(e){var t=this.$.appendChild(e);return this.updateView(!0),t}insertBefore(e,t){var n=this.$.insertBefore(e,t);return this.updateView(!0),n}appendUniChild(e){this.$children.push(e)}removeUniChild(e){var t=this.$children.indexOf(e);t>=0&&this.$children.splice(t,1)}removeUniParent(){var{$parent:e}=this;e&&(e.removeUniChild(this),this.$parent=void 0)}removeUniChildren(){this.$children.forEach((e=>e.remove())),this.$children.length=0}setWxsProps(e){Object.keys(e).forEach((t=>{if(0===t.indexOf(Lr)){var n=t.replace(Lr,""),r=pp(e[n]),i=dp(this,e[t],r);ip((()=>i(r)),4),this.$wxsProps.set(t,i),delete e[t],delete e[n],this.$hasWxsProps=!0}}))}addWxsEvents(e){Object.keys(e).forEach((t=>{var[n,r]=e[t];this.addWxsEvent(t,n,r)}))}addWxsEvent(e,t,n){}wxsPropsInvoke(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=this.$hasWxsProps&&this.$wxsProps.get(Lr+e);if(r)return ip((()=>n?_o((()=>r(t))):r(t)),4),!0}updateView(e){(this.isMounted||e)&&window.dispatchEvent(new CustomEvent("updateview"))}}function mp(e,t){var{__wxsAddClass:n,__wxsRemoveClass:r}=e;r&&r.length&&(t=t.split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),n&&n.length&&(t=t+" "+n.join(" ")),e.className=t}function _p(e){return Sp(Op(e))}var yp,bp,wp,xp=/url\(\s*'?"?([a-zA-Z0-9\.\-\_\/]+\.(jpg|gif|png))"?'?\s*\)/,Sp=e=>{if(pn(e)&&-1!==e.indexOf("url(")){var t=e.match(xp);t&&3===t.length&&(e=e.replace(t[1],mc(t[1])))}return e},{unit:kp,unitRatio:Tp,unitPrecision:Ep}={unit:"rem",unitRatio:10/320,unitPrecision:5},Cp=(yp=kp,bp=Tp,wp=Ep,e=>e.replace(_r,((e,t)=>{if(!t)return e;if(1===bp)return"".concat(t).concat(yp);var n,r,i,a,o=(n=parseFloat(t)*bp,r=wp,i=Math.pow(10,r+1),a=Math.floor(n*i),10*Math.round(a/10)/i);return 0===o?"0":"".concat(o).concat(yp)}))),Op=e=>pn(e)?Cp(e):e,Mp=["Webkit"],Lp={};function Ip(e,t){var n=Lp[t];if(n)return n;var r=En(t);if("filter"!==r&&r in e)return Lp[t]=r;r=Mn(r);for(var i=0;i<Mp.length;i++){var a=Mp[i]+r;if(a in e)return Lp[t]=a}return t}function Ap(e,t){var n=e.style;if(pn(t))""===t?e.removeAttribute("style"):n.cssText=_p(t);else for(var r in t)Np(n,r,t[r]);var{__wxsStyle:i}=e;if(i)for(var a in i)Np(n,a,i[a])}var Bp=/\s*!important$/;function Np(e,t,n){if(cn(n))n.forEach((n=>Np(e,t,n)));else if(n=_p(n),t.startsWith("--"))e.setProperty(t,n);else{var r=Ip(e,t);Bp.test(n)?e.setProperty(On(r),n.replace(Bp,""),"important"):e[r]=n}}function Rp(e,t){var n=e.__listeners[t];n&&e.removeEventListener(t,n)}function Pp(e,t){if(e.__listeners[t])return!0}function Dp(e,t,n){var[r,i]=Sr(t);-1===n?Rp(e,r):Pp(e,r)||e.addEventListener(r,e.__listeners[r]=zp(e.__id,n,i),i)}function zp(e,t,n){var r=t=>{var[r]=ac(t);r.type=function(e,t){return t&&(t.capture&&(e+="Capture"),t.once&&(e+="Once"),t.passive&&(e+="Passive")),"on".concat(Mn(En(e)))}(t.type,n),UniViewJSBridge.publishHandler(uc,[[Wr,e,r]])};return t?du(r,Fp(t)):r}function Fp(e){var t=[];return e&kr.prevent&&t.push("prevent"),e&kr.self&&t.push("self"),e&kr.stop&&t.push("stop"),t}function $p(e,t,n){var r=n=>{!function(e,t,n){var[r,i,a]=up(t),[o,s]=a.split("."),l=lp(e,r);cp(l,i,o,s,[hp(n,e),Ju(ec(l))])}(function(e){return!!e.addWxsEvent}(e)?e.$:e,t,ac(n)[0])};return n?du(r,Fp(n)):r}function jp(e,t){e._vod="none"===e.style.display?"":e.style.display,e.style.display=t?e._vod:"none"}class Vp extends gp{constructor(e,t,n,r,i){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[];super(e,t.tagName,n,t),this.$props=za({}),this.$.__id=e,this.$.__listeners=Object.create(null),this.$propNames=a,this._update=this.update.bind(this),this.init(i),this.insert(n,r)}init(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];un(e,"a")&&this.setAttrs(e.a),un(e,"s")&&this.setAttr("style",e.s),un(e,"e")&&this.addEvents(e.e),un(e,"w")&&this.addWxsEvents(e.w),super.init(e),t&&($o(this.$props,(()=>{ip(this._update,1)}),{flush:"sync"}),this.update(!0))}setAttrs(e){this.setWxsProps(e),Object.keys(e).forEach((t=>{this.setAttr(t,e[t])}))}addEvents(e){Object.keys(e).forEach((t=>{this.addEvent(t,e[t])}))}addWxsEvent(e,t,n){!function(e,t,n,r){var[i,a]=Sr(t);-1===r?Rp(e,i):Pp(e,i)||e.addEventListener(i,e.__listeners[i]=$p(e,n,r),a)}(this.$,e,t,n)}addEvent(e,t){Dp(this.$,e,t)}removeEvent(e){Dp(this.$,e,-1)}setAttr(e,t){e===Tr?mp(this.$,t):e===Er?Ap(this.$,t):e===Cr?jp(this.$,t):e===Or?this.$.__ownerId=t:e===Mr?ip((()=>fp(this,t)),3):"innerHTML"===e?this.$.innerHTML=t:"textContent"===e?this.setText(t):this.setAttribute(e,t),this.updateView()}removeAttr(e){e===Tr?mp(this.$,""):e===Er?Ap(this.$,""):this.removeAttribute(e),this.updateView()}setAttribute(e,t){t=pp(t,this.$),-1!==this.$propNames.indexOf(e)?this.$props[e]=t:vp(e)?this.$.style.setProperty(e,_p(t)):this.wxsPropsInvoke(e,t)||this.$.setAttribute(e,t)}removeAttribute(e){-1!==this.$propNames.indexOf(e)?delete this.$props[e]:vp(e)?this.$.style.removeProperty(e):this.$.removeAttribute(e)}update(){}}function Wp(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>"".concat(uni.upx2px(parseFloat(t)),"px"))):/^-?[\d\.]+$/.test(e)?"".concat(e,"px"):e||""}function Hp(e){var t=e.animation;if(t&&t.actions&&t.actions.length){var n=0,r=t.actions,i=t.actions.length;setTimeout((()=>{a()}),0)}function a(){var t=r[n],o=t.option.transition,s=function(e){var t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],r=["opacity","background-color"],i=["width","height","left","right","top","bottom"],a=e.animates,o=e.option,s=o.transition,l={},u=[];return a.forEach((e=>{var a=e.type,o=[...e.args];if(t.concat(n).includes(a))a.startsWith("rotate")||a.startsWith("skew")?o=o.map((e=>parseFloat(e)+"deg")):a.startsWith("translate")&&(o=o.map(Wp)),n.indexOf(a)>=0&&(o.length=1),u.push("".concat(a,"(").concat(o.join(","),")"));else if(r.concat(i).includes(o[0])){a=o[0];var s=o[1];l[a]=i.includes(a)?Wp(s):s}})),l.transform=l.webkitTransform=u.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>"".concat(function(e){return e.replace(/[A-Z]/g,(e=>"-".concat(e.toLowerCase()))).replace("webkit","-webkit")}(e)," ").concat(s.duration,"ms ").concat(s.timingFunction," ").concat(s.delay,"ms"))).join(","),l.transformOrigin=l.webkitTransformOrigin=o.transformOrigin,l}(t);Object.keys(s).forEach((t=>{e.$el.style[t]=s[t]})),(n+=1)<i&&setTimeout(a,o.duration+o.delay)}}const Up={props:["animation"],watch:{animation:{deep:!0,handler(){Hp(this)}}},mounted(){Hp(this)}};var qp=e=>{e.__reserved=!0;var{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Up),Yp(e)},Yp=e=>(e.__reserved=!0,e.compatConfig={MODE:3},Yo(e));function Xp(e){return e.__wwe=!0,e}function Zp(e,t){return(n,r,i)=>{e.value&&t(n,function(e,t,n,r){var i;return i=br(n),{type:r.type||e,timeStamp:t.timeStamp||0,target:i,currentTarget:i,detail:r}}(n,r,e.value,i||{}))}}function Gp(e){var t,n,r=Qa(!1),i=!1;function a(){requestAnimationFrame((()=>{clearTimeout(n),n=setTimeout((()=>{r.value=!1}),parseInt(e.hoverStayTime))}))}function o(n){n._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(n._hoverPropagationStopped=!0),i=!0,t=setTimeout((()=>{r.value=!0,i||a()}),parseInt(e.hoverStartTime)))}function s(){i=!1,r.value&&a()}function l(){s(),window.removeEventListener("mouseup",l)}return{hovering:r,binding:{onTouchstartPassive:Xp((function(e){e.touches.length>1||o(e)})),onMousedown:Xp((function(e){i||(o(e),window.addEventListener("mouseup",l))})),onTouchend:Xp((function(){s()})),onMouseup:Xp((function(){i&&l()})),onTouchcancel:Xp((function(){i=!1,r.value=!1,clearTimeout(t)}))}}}function Kp(e,t){return pn(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}var Jp=Cu("uf");const Qp=qp({name:"Form",emits:["submit","reset"],setup(e,t){var n,r,{slots:i,emit:a}=t,o=Qa(null);return n=Zp(o,a),r=[],Bs(Jp,{addField(e){r.push(e)},removeField(e){r.splice(r.indexOf(e),1)},submit(e){n("submit",e,{value:r.reduce(((e,t)=>{if(t.submit){var[n,r]=t.submit();n&&(e[n]=r)}return e}),Object.create(null))})},reset(e){r.forEach((e=>e.reset&&e.reset())),n("reset",e)}}),()=>vl("uni-form",{ref:o},[vl("span",null,[i.default&&i.default()])],512)}});var ev={for:{type:String,default:""}},tv=Cu("ul");const nv=qp({name:"Label",props:ev,setup(e,t){var{slots:n}=t,r=Qa(null),i=Bu(),a=function(){var e=[];return Bs(tv,{addHandler(t){e.push(t)},removeHandler(t){e.splice(e.indexOf(t),1)}}),e}(),o=Fl((()=>e.for||n.default&&n.default.length)),s=Xp((t=>{var n=t.target,r=/^uni-(checkbox|radio|switch)-/.test(n.className);r||(r=/^uni-(checkbox|radio|switch|button)$|^(svg|path)$/i.test(n.tagName)),r||(e.for?UniViewJSBridge.emit("uni-label-click-"+i+"-"+e.for,t,!0):a.length&&a[0](t,!0))}));return()=>vl("uni-label",{ref:r,class:{"uni-label-pointer":o},onClick:s},[n.default&&n.default()],10,["onClick"])}});function rv(e,t){iv(e.id,t),$o((()=>e.id),((e,n)=>{av(n,t,!0),iv(e,t,!0)})),ss((()=>{av(e.id,t)}))}function iv(e,t,n){var r=Bu();n&&!e||wn(t)&&Object.keys(t).forEach((i=>{n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&UniViewJSBridge.on("uni-".concat(i,"-").concat(r,"-").concat(e),t[i]):0===i.indexOf("uni-")?UniViewJSBridge.on(i,t[i]):e&&UniViewJSBridge.on("uni-".concat(i,"-").concat(r,"-").concat(e),t[i])}))}function av(e,t,n){var r=Bu();n&&!e||wn(t)&&Object.keys(t).forEach((i=>{n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&UniViewJSBridge.off("uni-".concat(i,"-").concat(r,"-").concat(e),t[i]):0===i.indexOf("uni-")?UniViewJSBridge.off(i,t[i]):e&&UniViewJSBridge.off("uni-".concat(i,"-").concat(r,"-").concat(e),t[i])}))}const ov=qp({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,t){var{slots:n}=t,r=Qa(null);di();var i=Ns(Jp,!1),{hovering:a,binding:o}=Gp(e),{t:s}=li(),l=Xp(((t,n)=>{if(e.disabled)return t.stopImmediatePropagation();n&&r.value.click();var a=e.formType;if(a){if(!i)return;"submit"===a?i.submit(t):"reset"===a&&i.reset(t)}else{var o,l,u;"feedback"===e.openType&&(o=s("uni.button.feedback.title"),l=s("uni.button.feedback.send"),(u=plus.webview.create("https://service.dcloud.net.cn/uniapp/feedback.html","feedback",{titleNView:{titleText:o,autoBackButton:!0,backgroundColor:"#F7F7F7",titleColor:"#007aff",buttons:[{text:l,color:"#007aff",fontSize:"16px",fontWeight:"bold",onclick:function(){u.evalJS('typeof mui !== "undefined" && mui.trigger(document.getElementById("submit"),"tap")')}}]}})).show("slide-in-right"))}})),u=Ns(tv,!1);return u&&(u.addHandler(l),os((()=>{u.removeHandler(l)}))),rv(e,{"label-click":l}),()=>{var t=e.hoverClass,i=Kp(e,"disabled"),s=Kp(e,"loading"),u=Kp(e,"plain"),c=t&&"none"!==t;return vl("uni-button",wl({ref:r,onClick:l,id:e.id,class:c&&a.value?t:""},c&&o,i,s,u),[n.default&&n.default()],16,["onClick","id"])}}});const sv=qp({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,t){var{emit:n}=t,r=Qa(null),i=function(e){return()=>{var{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(r),a=function(e,t,n){var r=za({width:-1,height:-1});return $o((()=>on({},r)),(e=>t("resize",e))),()=>{var t=e.value;t&&(r.width=t.offsetWidth,r.height=t.offsetHeight,n())}}(r,n,i);return function(e,t,n,r){Go(r),rs((()=>{t.initial&&_o(n);var i=e.value;i.offsetParent!==i.parentElement&&(i.parentElement.style.position="relative"),"AnimationEvent"in window||r()}))}(r,e,a,i),()=>vl("uni-resize-sensor",{ref:r,onAnimationstartOnce:a},[vl("div",{onScroll:a},[vl("div",null,null)],40,["onScroll"]),vl("div",{onScroll:a},[vl("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});var lv=function(){var e=document.createElement("canvas");e.height=e.width=0;var t=e.getContext("2d"),n=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}();function uv(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=t?lv:1;e.width=e.offsetWidth*n,e.height=e.offsetHeight*n,e.getContext("2d").__hidpi__=t}var cv=!1;var dv,hv=fr((()=>function(){if(!cv){cv=!0;var e=CanvasRenderingContext2D.prototype;e.drawImageByCanvas=function(e){return function(t,n,r,i,a,o,s,l,u,c){if(!this.__hidpi__)return e.apply(this,arguments);n*=lv,r*=lv,i*=lv,a*=lv,o*=lv,s*=lv,l=c?l*lv:l,u=c?u*lv:u,e.call(this,t,n,r,i,a,o,s,l,u)}}(e.drawImage),1!==lv&&(function(e,t){for(var n in e)un(e,n)&&t(e[n],n)}({fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",transform:[4,5],setTransform:[4,5]},(function(t,n){e[n]=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var n=Array.prototype.slice.call(arguments);if("all"===t)n=n.map((function(e){return e*lv}));else if(Array.isArray(t))for(var r=0;r<t.length;r++)n[t[r]]*=lv;return e.apply(this,n)}}(e[n])})),e.stroke=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.lineWidth*=lv,e.apply(this,arguments),this.lineWidth/=lv}}(e.stroke),e.fillText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=lv,t[2]*=lv,t[3]&&"number"==typeof t[3]&&(t[3]*=lv);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*lv+n})),e.apply(this,t),this.font=n}}(e.fillText),e.strokeText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=lv,t[2]*=lv,t[3]&&"number"==typeof t[3]&&(t[3]*=lv);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*lv+n})),e.apply(this,t),this.font=n}}(e.strokeText),e.drawImage=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.scale(lv,lv),e.apply(this,arguments),this.scale(1/lv,1/lv)}}(e.drawImage))}}()));function fv(e){return e?mc(e):e}function pv(e){return(e=e.slice(0))[3]=e[3]/255,"rgba("+e.join(",")+")"}function vv(e,t){Array.from(t).forEach((t=>{t.x=t.clientX-e.left,t.y=t.clientY-e.top}))}function gv(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return dv||(dv=document.createElement("canvas")),dv.width=e,dv.height=t,dv}const mv=qp({inheritAttrs:!1,name:"Canvas",compatConfig:{MODE:3},props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1},hidpi:{type:Boolean,default:!0}},computed:{id(){return this.canvasId}},setup(e,t){var{emit:n,slots:r}=t;hv();var i=Qa(null),a=Qa(null),o=Qa(null),s=Qa(!1),l=function(e){return(t,n)=>{e(t,oc(n))}}(n),{$attrs:u,$excludeAttrs:c,$listeners:d}=Pg({excludeListeners:!0}),{_listeners:h}=function(e,t,n){var r=Fl((()=>{var r=["onTouchstart","onTouchmove","onTouchend"],i=t.value,a=on({},(()=>{var e={};for(var t in i)if(un(i,t)){var n=i[t];e[t]=n}return e})());return r.forEach((t=>{var r=[];a[t]&&r.push(Xp((e=>{var r=e.currentTarget.getBoundingClientRect();vv(r,e.touches),vv(r,e.changedTouches),n(t.replace("on","").toLocaleLowerCase(),e)}))),e.disableScroll&&"onTouchmove"===t&&r.push(ku),a[t]=r})),a}));return{_listeners:r}}(e,d,l),{_handleSubscribe:f,_resize:p}=function(e,t,n){var r=[],i={},a=Fl((()=>e.hidpi?lv:1));function o(n){var r=t.value;if(!n||r.width!==Math.floor(n.width*a.value)||r.height!==Math.floor(n.height*a.value))if(r.width>0&&r.height>0){var i=r.getContext("2d"),o=i.getImageData(0,0,r.width,r.height);uv(r,e.hidpi),i.putImageData(o,0,0)}else uv(r,e.hidpi)}function s(e,a){var{actions:o,reserve:s}=e;if(o)if(n.value)r.push([o,s]);else{var c=t.value,d=c.getContext("2d");s||(d.fillStyle="#000000",d.strokeStyle="#000000",d.shadowColor="#000000",d.shadowBlur=0,d.shadowOffsetX=0,d.shadowOffsetY=0,d.setTransform(1,0,0,1,0,0),d.clearRect(0,0,c.width,c.height)),l(o);for(var h=function(e){var t=o[e],n=t.method,r=t.data,s=r[0];if(/^set/.test(n)&&"setTransform"!==n){var l,c=n[3].toLowerCase()+n.slice(4);if("fillStyle"===c||"strokeStyle"===c){if("normal"===s)l=pv(r[1]);else if("linear"===s){var h=d.createLinearGradient(...r[1]);r[2].forEach((function(e){var t=e[0],n=pv(e[1]);h.addColorStop(t,n)})),l=h}else if("radial"===s){var f=r[1],p=f[0],v=f[1],g=f[2],m=d.createRadialGradient(p,v,0,p,v,g);r[2].forEach((function(e){var t=e[0],n=pv(e[1]);m.addColorStop(t,n)})),l=m}else if("pattern"===s){return u(r[1],o.slice(e+1),a,(function(e){e&&(d[c]=d.createPattern(e,r[2]))}))?"continue":"break"}d[c]=l}else if("globalAlpha"===c)d[c]=Number(s)/255;else if("shadow"===c){var _=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"];r.forEach((function(e,t){d[_[t]]="shadowColor"===_[t]?pv(e):e}))}else if("fontSize"===c){var y=d.__font__||d.font;d.__font__=d.font=y.replace(/\d+\.?\d*px/,s+"px")}else"lineDash"===c?(d.setLineDash(s),d.lineDashOffset=r[1]||0):"textBaseline"===c?("normal"===s&&(r[0]="alphabetic"),d[c]=s):"font"===c?d.__font__=d.font=s:d[c]=s}else if("fillPath"===n||"strokePath"===n)n=n.replace(/Path/,""),d.beginPath(),r.forEach((function(e){d[e.method].apply(d,e.data)})),d[n]();else if("fillText"===n)d.fillText.apply(d,r);else if("drawImage"===n){if("break"===function(){var t=[...r],n=t[0],s=t.slice(1);if(i=i||{},!u(n,o.slice(e+1),a,(function(e){e&&d.drawImage.apply(d,[e].concat([...s.slice(4,8)],[...s.slice(0,4)]))})))return"break"}())return"break"}else"clip"===n?(r.forEach((function(e){d[e.method].apply(d,e.data)})),d.clip()):d[n].apply(d,r)},f=0;f<o.length;f++){var p=h(f);if("break"===p)break}n.value||a({errMsg:"drawCanvas:ok"})}}function l(e){e.forEach((function(e){var t=e.method,n=e.data,r="";function a(){var e=i[r]=new Image;if(e.onload=function(){e.ready=!0},"Google Inc."===navigator.vendor)return 0===r.indexOf("file://")&&(e.crossOrigin="anonymous"),void(e.src=r);wc(r).then((t=>{e.src=t})).catch((()=>{e.src=r}))}"drawImage"===t?(r=fv(r=n[0]),n[0]=r):"setFillStyle"===t&&"pattern"===n[0]&&(r=fv(r=n[1]),n[1]=r),r&&!i[r]&&a()}))}function u(e,t,a,o){var l=i[e];return l.ready?(o(l),!0):(r.unshift([t,!0]),n.value=!0,l.onload=function(){l.ready=!0,o(l),n.value=!1;var e=r.slice(0);r=[];for(var t=e.shift();t;)s({actions:t[0],reserve:t[1]},a),t=e.shift()},!1)}function c(e,n){var r,{x:i=0,y:o=0,width:s,height:l,destWidth:u,destHeight:c,hidpi:d=!0,dataType:h,quality:f=1,type:p="png"}=e,v=t.value,g=v.offsetWidth-i;s=s?Math.min(s,g):g;var m=v.offsetHeight-o;l=l?Math.min(l,m):m,d?(u=s,c=l):u||c?u?c||(c=Math.round(l/s*u)):(c||(c=Math.round(l*a.value)),u=Math.round(s/l*c)):(u=Math.round(s*a.value),c=Math.round(l*a.value));var _,y=gv(u,c),b=y.getContext("2d");"jpeg"!==p&&"jpg"!==p||(p="jpeg",b.fillStyle="#fff",b.fillRect(0,0,u,c)),b.__hidpi__=!0,b.drawImageByCanvas(v,i,o,s,l,0,0,u,c,!1);try{var w;if("base64"===h)r=y.toDataURL("image/".concat(p),f);else{var x=b.getImageData(0,0,u,c);r=Lf.deflateRaw(x.data,{to:"string"}),w=!0}_={data:r,compressed:w,width:u,height:c}}catch(S){_={errMsg:"canvasGetImageData:fail ".concat(S)}}if(y.height=y.width=0,b.__hidpi__=!1,!n)return _;n(_)}function d(e,n){var{data:r,x:i,y:a,width:o,height:s,compressed:l}=e;try{l&&(r=Lf.inflateRaw(r)),s||(s=Math.round(r.length/4/o));var u=gv(o,s);u.getContext("2d").putImageData(new ImageData(new Uint8ClampedArray(r),o,s),0,0),t.value.getContext("2d").drawImage(u,i,a,o,s),u.height=u.width=0}catch(c){return void n({errMsg:"canvasPutImageData:fail"})}n({errMsg:"canvasPutImageData:ok"})}function h(e,t){var{x:n=0,y:r=0,width:i,height:a,destWidth:o,destHeight:s,fileType:l,quality:u,dirname:d}=e,h=c({x:n,y:r,width:i,height:a,destWidth:o,destHeight:s,hidpi:!1,dataType:"base64",type:l,quality:u});h.errMsg?t({errMsg:h.errMsg.replace("canvasPutImageData","toTempFilePath")}):function(e,t,n){var r="".concat(Date.now()).concat(yc++),i=e.split(","),a=i[0],o=i[1],s=(a.match(/data:image\/(\S+?);/)||["","png"])[1].replace("jpeg","jpg"),l="".concat(r,".").concat(s),u="".concat(t,"/").concat(l),c=t.indexOf("/"),d=t.substring(0,c),h=t.substring(c+1);plus.io.resolveLocalFileSystemURL(d,(function(e){e.getDirectory(h,{create:!0,exclusive:!1},(function(e){e.getFile(l,{create:!0,exclusive:!1},(function(e){e.createWriter((function(e){e.onwrite=function(){n(null,u)},e.onerror=n,e.seek(0),e.writeAsBinary(o)}),n)}),n)}),n)}),n)}(h.data,d,((e,n)=>{var r="toTempFilePath:".concat(e?"fail":"ok");e&&(r+=" ".concat(e.message)),t({errMsg:r,tempFilePath:n})}))}var f={actionsChanged:s,getImageData:c,putImageData:d,toTempFilePath:h};function p(e,t,n){var r=f[e];0!==e.indexOf("_")&&fn(r)&&r(t,n)}return on(f,{_resize:o,_handleSubscribe:p})}(e,a,s);return t_(f,r_(e.canvasId),!0),rs((()=>{p()})),()=>{var{canvasId:t,disableScroll:n}=e;return vl("uni-canvas",wl({ref:i,"canvas-id":t,"disable-scroll":n},u.value,c.value,h.value),[vl("canvas",{ref:a,class:"uni-canvas-canvas",width:"300",height:"150"},null,512),vl("div",{style:"position: absolute;top: 0;left: 0;width: 100%;height: 100%;overflow: hidden;"},[r.default&&r.default()]),vl(sv,{ref:o,onResize:p},null,8,["onResize"])],16,["canvas-id","disable-scroll"])}}});var _v=Cu("ucg");const yv=qp({name:"CheckboxGroup",props:{name:{type:String,default:""}},emits:["change"],setup(e,t){var{emit:n,slots:r}=t,i=Qa(null);return function(e,t){var n=[],r=()=>n.reduce(((e,t)=>(t.value.checkboxChecked&&e.push(t.value.value),e)),new Array);Bs(_v,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},checkboxChange(e){t("change",e,{value:r()})}});var i=Ns(Jp,!1);i&&i.addField({submit:()=>{var t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=r()),t}})}(e,Zp(i,n)),()=>vl("uni-checkbox-group",{ref:i},[r.default&&r.default()],512)}});const bv=qp({name:"Checkbox",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:""},foreColor:{type:String,default:""}},setup(e,t){var{slots:n}=t,r=Qa(null),i=Qa(e.checked),a=Fl((()=>"true"===i.value||!0===i.value)),o=Qa(e.value);var s=Fl((()=>function(t){if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};var n={};return t?(e.activeBorderColor&&(n.borderColor=e.activeBorderColor),e.activeBackgroundColor&&(n.backgroundColor=e.activeBackgroundColor)):(e.borderColor&&(n.borderColor=e.borderColor),e.backgroundColor&&(n.backgroundColor=e.backgroundColor)),n}(a.value)));$o([()=>e.checked,()=>e.value],(e=>{var[t,n]=e;i.value=t,o.value=n}));var{uniCheckGroup:l,uniLabel:u}=function(e,t,n){var r=Fl((()=>({checkboxChecked:Boolean(e.value),value:t.value}))),i={reset:n},a=Ns(_v,!1);a&&a.addField(r);var o=Ns(Jp,!1);o&&o.addField(i);var s=Ns(tv,!1);return os((()=>{a&&a.removeField(r),o&&o.removeField(i)})),{uniCheckGroup:a,uniForm:o,uniLabel:s}}(i,o,(()=>{i.value=!1})),c=t=>{e.disabled||(i.value=!i.value,l&&l.checkboxChange(t),t.stopPropagation())};return u&&(u.addHandler(c),os((()=>{u.removeHandler(c)}))),rv(e,{"label-click":c}),()=>{var t,a=Kp(e,"disabled");return t=i.value,vl("uni-checkbox",wl(a,{id:e.id,onClick:c,ref:r}),[vl("div",{class:"uni-checkbox-wrapper",style:{"--HOVER-BD-COLOR":e.activeBorderColor}},[vl("div",{class:["uni-checkbox-input",{"uni-checkbox-input-disabled":e.disabled}],style:s.value},[t?Au(Iu,e.disabled?"#ADADAD":e.foreColor||e.iconColor||e.color,22):""],6),n.default&&n.default()],4)],16,["id","onClick"])}}});var wv,xv,Sv,kv,Tv,Ev;function Cv(){}function Ov(e,t,n){wr((()=>{var r="adjustResize",i="adjustPan",a=plus.webview.currentWebview(),o=Ev||a.getStyle()||{},s={mode:n||o.softinputMode===r?r:e.adjustPosition?i:"nothing",position:{top:0,height:0}};if(s.mode===i){var l=t.getBoundingClientRect();s.position.top=l.top,s.position.height=l.height+(Number(e.cursorSpacing)||0)}a.setSoftinputTemporary(s)}))}wr((()=>{xv="Android"===plus.os.name,Sv=plus.os.version||""})),document.addEventListener("keyboardchange",(function(e){kv=e.height,Tv&&Tv()}),!1);var Mv={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}},Lv=["keyboardheightchange"];function Iv(e,t,n){var r={};function i(t){var i,a=Fl((()=>0===String(navigator.vendor).indexOf("Apple"))),o=()=>{n("keyboardheightchange",{},{height:kv,duration:.25}),i&&0===kv&&Ov(e,t),e.autoBlur&&i&&0===kv&&(xv||parseInt(Sv)>=13)&&document.activeElement.blur()};t.addEventListener("focus",(()=>{i=!0,clearTimeout(wv),document.addEventListener("click",Cv,!1),Tv=o,kv&&n("keyboardheightchange",{},{height:kv,duration:0}),function(e,t){"auto"!==e.showConfirmBar?wr((()=>{var n=plus.webview.currentWebview(),{softinputNavBar:r}=n.getStyle()||{};"none"!==r!==e.showConfirmBar?(t.softinputNavBar=r||"auto",n.setStyle({softinputNavBar:e.showConfirmBar?"auto":"none"})):delete t.softinputNavBar})):delete t.softinputNavBar}(e,r),Ov(e,t)})),xv&&t.addEventListener("click",(()=>{e.disabled||e.readOnly||!i||0!==kv||Ov(e,t)})),xv||(parseInt(Sv)<12&&t.addEventListener("touchstart",(()=>{e.disabled||e.readOnly||i||Ov(e,t)})),parseFloat(Sv)>=14.6&&!Ev&&wr((()=>{var e=plus.webview.currentWebview();Ev=e.getStyle()||{}})));var s=()=>{document.removeEventListener("click",Cv,!1),Tv=null,kv&&n("keyboardheightchange",{},{height:0,duration:0}),function(e){var t=e.softinputNavBar;t&&wr((()=>{plus.webview.currentWebview().setStyle({softinputNavBar:t})}))}(r),xv&&(wv=setTimeout((()=>{Ov(e,t,!0)}),300)),a.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)};t.addEventListener("blur",(()=>{a.value&&t.blur(),i=!1,s()}))}$o((()=>t.value),(e=>e&&i(e)))}var Av=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,Bv=/^<\/([-A-Za-z0-9_]+)[^>]*>/,Nv=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,Rv=Vv("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),Pv=Vv("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),Dv=Vv("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),zv=Vv("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),Fv=Vv("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),$v=Vv("script,style");function jv(e,t){var n,r,i,a=[],o=e;for(a.last=function(){return this[this.length-1]};e;){if(r=!0,a.last()&&$v[a.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+a.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),u("",a.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),r=!1):0==e.indexOf("</")?(i=e.match(Bv))&&(e=e.substring(i[0].length),i[0].replace(Bv,u),r=!1):0==e.indexOf("<")&&(i=e.match(Av))&&(e=e.substring(i[0].length),i[0].replace(Av,l),r=!1),r){var s=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(s)}if(e==o)throw"Parse Error: "+e;o=e}function l(e,n,r,i){if(n=n.toLowerCase(),Pv[n])for(;a.last()&&Dv[a.last()];)u("",a.last());if(zv[n]&&a.last()==n&&u("",n),(i=Rv[n]||!!i)||a.push(n),t.start){var o=[];r.replace(Nv,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:Fv[t]?t:"";o.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,o,i)}}function u(e,n){if(n)for(r=a.length-1;r>=0&&a[r]!=n;r--);else var r=0;if(r>=0){for(var i=a.length-1;i>=r;i--)t.end&&t.end(a[i]);a.length=r}}u()}function Vv(e){for(var t={},n=e.split(","),r=0;r<n.length;r++)t[n[r]]=!0;return t}var Wv={};function Hv(e,t,n){if(pn(e)?window[e]:e)n();else{var r=Wv[t];if(!r){r=Wv[t]=[];var i=document.createElement("script");i.src=t,document.body.appendChild(i),i.onload=function(){r.forEach((e=>e())),delete Wv[t]}}r.push(n)}}function Uv(e){var t=e.import("blots/block/embed");class n extends t{}return n.blotName="divider",n.tagName="HR",{"formats/divider":n}}function qv(e){var t=e.import("blots/inline");class n extends t{}return n.blotName="ins",n.tagName="INS",{"formats/ins":n}}function Yv(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.BLOCK,whitelist:["left","right","center","justify"]};return{"formats/align":new n.Style("align","text-align",r)}}function Xv(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.BLOCK,whitelist:["rtl"]};return{"formats/direction":new n.Style("direction","direction",r)}}function Zv(e){var t=e.import("parchment"),n=e.import("blots/container"),r=e.import("formats/list/item");class i extends n{static create(e){var t="ordered"===e?"OL":"UL",n=super.create(t);return"checked"!==e&&"unchecked"!==e||n.setAttribute("data-checked","checked"===e),n}static formats(e){return"OL"===e.tagName?"ordered":"UL"===e.tagName?e.hasAttribute("data-checked")?"true"===e.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}constructor(e){super(e);e.addEventListener("click",(n=>{if(n.target.parentNode===e){var r=this.statics.formats(e),i=t.find(n.target);"checked"===r?i.format("list","unchecked"):"unchecked"===r&&i.format("list","checked")}}))}format(e,t){this.children.length>0&&this.children.tail.format(e,t)}formats(){return{[this.statics.blotName]:this.statics.formats(this.domNode)}}insertBefore(e,t){if(e instanceof r)super.insertBefore(e,t);else{var n=null==t?this.length():t.offset(this),i=this.split(n);i.parent.insertBefore(e,i)}}optimize(e){super.optimize(e);var t=this.next;null!=t&&t.prev===this&&t.statics.blotName===this.statics.blotName&&t.domNode.tagName===this.domNode.tagName&&t.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(t.moveChildren(this),t.remove())}replace(e){if(e.statics.blotName!==this.statics.blotName){var n=t.create(this.statics.defaultChild);e.moveChildren(n),this.appendChild(n)}super.replace(e)}}return i.blotName="list",i.scope=t.Scope.BLOCK_BLOT,i.tagName=["OL","UL"],i.defaultChild="list-item",i.allowedChildren=[r],{"formats/list":i}}function Gv(e){var{Scope:t}=e.import("parchment");return{"formats/backgroundColor":new(e.import("formats/background").constructor)("backgroundColor","background-color",{scope:t.INLINE})}}function Kv(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.BLOCK},i={};return["margin","marginTop","marginBottom","marginLeft","marginRight"].concat(["padding","paddingTop","paddingBottom","paddingLeft","paddingRight"]).forEach((e=>{i["formats/".concat(e)]=new n.Style(e,On(e),r)})),i}function Jv(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.INLINE},i={};return["font","fontSize","fontStyle","fontVariant","fontWeight","fontFamily"].forEach((e=>{i["formats/".concat(e)]=new n.Style(e,On(e),r)})),i}function Qv(e){var{Scope:t,Attributor:n}=e.import("parchment"),r=[{name:"lineHeight",scope:t.BLOCK},{name:"letterSpacing",scope:t.INLINE},{name:"textDecoration",scope:t.INLINE},{name:"textIndent",scope:t.BLOCK}],i={};return r.forEach((e=>{var{name:t,scope:r}=e;i["formats/".concat(t)]=new n.Style(t,On(t),{scope:r})})),i}function eg(e){var t=e.import("formats/image"),n=["alt","height","width","data-custom","class","data-local"];t.sanitize=e=>e?mc(e):e,t.formats=function(e){return n.reduce((function(t,n){return e.hasAttribute(n)&&(t[n]=e.getAttribute(n)),t}),{})};var r=t.prototype.format;t.prototype.format=function(e,t){n.indexOf(e)>-1?t?this.domNode.setAttribute(e,t):this.domNode.removeAttribute(e):r.call(this,e,t)}}function tg(e){var t=e.import("formats/link");t.sanitize=e=>{var n=document.createElement("a");n.href=e;var r=n.href.slice(0,n.href.indexOf(":"));return t.PROTOCOL_WHITELIST.concat("file").indexOf(r)>-1?e:t.SANITIZED_URL}}function ng(e,t,n){var r,i,a;function o(){return{html:a.root.innerHTML,text:a.getText(),delta:a.getContents()}}function s(e){var t="data-placeholder",n=a.root;n.getAttribute(t)!==e&&n.setAttribute(t,e)}$o((()=>e.readOnly),(e=>{r&&(a.enable(!e),e||a.blur())})),$o((()=>e.placeholder),(e=>{r&&s(e)}));var l={};function u(e){var t=e?a.getFormat(e):{},r=Object.keys(t);(r.length!==Object.keys(l).length||r.find((e=>t[e]!==l[e])))&&(l=t,n("statuschange",{},t))}function c(){n("input",{},o())}function d(l){var d=window.Quill;!function(e){var t={divider:Uv,ins:qv,align:Yv,direction:Xv,list:Zv,background:Gv,box:Kv,font:Jv,text:Qv,image:eg,link:tg},n={};Object.values(t).forEach((t=>on(n,t(e)))),e.register(n,!0)}(d);var h={toolbar:!1,readOnly:e.readOnly,placeholder:e.placeholder};l.length&&(d.register("modules/ImageResize",window.ImageResize.default),h.modules={ImageResize:{modules:l}});var f=t.value,p=(a=new d(f,h)).root;["focus","blur","input"].forEach((t=>{p.addEventListener(t,(r=>{var i=o();if("input"===t){if("ios"===gc().platform){var a=(i.html.match(/<span [\s\S]*>([\s\S]*)<\/span>/)||[])[1];s(a&&a.replace(/\s/g,"")?"":e.placeholder)}r.stopPropagation()}else n(t,r,i)}))})),a.on("text-change",c),a.on("selection-change",u),a.on("scroll-optimize",(()=>{u(a.selection.getRange()[0])})),a.clipboard.addMatcher(Node.ELEMENT_NODE,((e,t)=>(i||t.ops&&(t.ops=t.ops.filter((e=>{var{insert:t}=e;return pn(t)})).map((e=>{var{insert:t}=e;return{insert:t}}))),t))),r=!0,n("ready",{},{})}t_(((e,t,n)=>{var s,l,d,{options:h,callbackId:f}=t;if(r){var p=window.Quill;switch(e){case"format":var{name:v="",value:g=!1}=h;l=a.getSelection(!0);var m=a.getFormat(l)[v]||!1;if(["bold","italic","underline","strike","ins"].includes(v))g=!m;else if("direction"===v){g=("rtl"!==g||!m)&&g;var _=a.getFormat(l).align;"rtl"!==g||_?g||"right"!==_||a.format("align",!1,"user"):a.format("align","right","user")}else if("indent"===v){g="+1"===g,"rtl"===a.getFormat(l).direction&&(g=!g),g=g?"+1":"-1"}else"list"===v&&(g="check"===g?"unchecked":g,m="checked"===m?"unchecked":m),g=m&&m!==(g||!1)||!m&&g?g:!m;a.format(v,g,"user");break;case"insertDivider":l=a.getSelection(!0),a.insertText(l.index,Jn,"user"),a.insertEmbed(l.index+1,"divider",!0,"user"),a.setSelection(l.index+2,0,"silent");break;case"insertImage":l=a.getSelection(!0);var{src:y="",alt:b="",width:w="",height:x="",extClass:S="",data:k={}}=h,T=mc(y);a.insertEmbed(l.index,"image",T,"silent");var E=!!/^(file|blob):/.test(T)&&T;a.formatText(l.index,1,"data-local",E,"silent"),a.formatText(l.index,1,"alt",b,"silent"),a.formatText(l.index,1,"width",w,"silent"),a.formatText(l.index,1,"height",x,"silent"),a.formatText(l.index,1,"class",S,"silent"),a.formatText(l.index,1,"data-custom",Object.keys(k).map((e=>"".concat(e,"=").concat(k[e]))).join("&"),"silent"),a.setSelection(l.index+1,0,"silent"),a.scrollIntoView(),setTimeout((()=>{c()}),1e3);break;case"insertText":l=a.getSelection(!0);var{text:C=""}=h;a.insertText(l.index,C,"user"),a.setSelection(l.index+C.length,0,"silent");break;case"setContents":var{delta:O,html:M}=h;"object"==typeof O?a.setContents(O,"silent"):pn(M)?a.setContents(function(e){var t,n=["span","strong","b","ins","em","i","u","a","del","s","sub","sup","img","div","p","h1","h2","h3","h4","h5","h6","hr","ol","ul","li","br"],r="";jv(e,{start:function(e,i,a){if(n.includes(e)){t=!1;var o=i.map((e=>{var{name:t,value:n}=e;return"".concat(t,'="').concat(n,'"')})).join(" "),s="<".concat(e," ").concat(o," ").concat(a?"/":"",">");r+=s}else t=!a},end:function(e){t||(r+="</".concat(e,">"))},chars:function(e){t||(r+=e)}}),i=!0;var o=a.clipboard.convert(r);return i=!1,o}(M),"silent"):d="contents is missing";break;case"getContents":s=o();break;case"clear":a.setText("");break;case"removeFormat":l=a.getSelection(!0);var L=p.import("parchment");l.length?a.removeFormat(l.index,l.length,"user"):Object.keys(a.getFormat(l)).forEach((e=>{L.query(e,L.Scope.INLINE)&&a.format(e,!1)}));break;case"undo":a.history.undo();break;case"redo":a.history.redo();break;case"blur":a.blur();break;case"getSelectionText":s={text:""},(l=a.selection.savedRange)&&0!==l.length&&(s.text=a.getText(l.index,l.length));break;case"scrollIntoView":a.scrollIntoView()}u(l)}else d="not ready";f&&n({callbackId:f,data:on({},s,{errMsg:"".concat(e,":").concat(d?"fail "+d:"ok")})})}),r_(),!0),rs((()=>{var t=[];e.showImgSize&&t.push("DisplaySize"),e.showImgToolbar&&t.push("Toolbar"),e.showImgResize&&t.push("Resize");Hv(window.Quill,"./__uniappquill.js",(()=>{if(t.length){Hv(window.ImageResize,"./__uniappquillimageresize.js",(()=>{d(t)}))}else d(t)}))}))}const rg=qp({name:"Editor",props:on({},Mv,{id:{type:String,default:""},readOnly:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},showImgSize:{type:[Boolean,String],default:!1},showImgToolbar:{type:[Boolean,String],default:!1},showImgResize:{type:[Boolean,String],default:!1}}),emit:["ready","focus","blur","input","statuschange",...Lv],setup(e,t){var{emit:n}=t,r=Qa(null),i=Zp(r,n);return ng(e,r,i),Iv(e,r,i),()=>vl("uni-editor",{ref:r,id:e.id,class:"ql-container"},null,8,["id"])}});var ig="#10aeff",ag="#b2b2b2",og={success:{d:"M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM24.832 11.328l-11.264 11.104q-0.032 0.032-0.112 0.032t-0.112-0.032l-5.216-5.376q-0.096-0.128 0-0.288l0.704-0.96q0.032-0.064 0.112-0.064t0.112 0.032l4.256 3.264q0.064 0.032 0.144 0.032t0.112-0.032l10.336-8.608q0.064-0.064 0.144-0.064t0.112 0.064l0.672 0.672q0.128 0.128 0 0.224z",c:er},success_no_circle:{d:Iu,c:er},info:{d:"M15.808 0.128q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.176 3.776-2.176 8.16 0 4.224 2.176 7.872 2.080 3.552 5.632 5.632 3.648 2.176 7.872 2.176 4.384 0 8.16-2.176 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.416-2.176-8.16-2.112-3.616-5.728-5.728-3.744-2.176-8.16-2.176zM16.864 23.776q0 0.064-0.064 0.064h-1.568q-0.096 0-0.096-0.064l-0.256-11.328q0-0.064 0.064-0.064h2.112q0.096 0 0.064 0.064l-0.256 11.328zM16 10.88q-0.576 0-0.976-0.4t-0.4-0.96 0.4-0.96 0.976-0.4 0.976 0.4 0.4 0.96-0.4 0.96-0.976 0.4z",c:ig},warn:{d:"M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",c:"#f76260"},waiting:{d:"M15.84 0.096q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM23.008 21.92l-0.512 0.896q-0.096 0.128-0.224 0.064l-8-3.808q-0.096-0.064-0.16-0.128-0.128-0.096-0.128-0.288l0.512-12.096q0-0.064 0.048-0.112t0.112-0.048h1.376q0.064 0 0.112 0.048t0.048 0.112l0.448 10.848 6.304 4.256q0.064 0.064 0.080 0.128t-0.016 0.128z",c:ig},cancel:{d:"M20.928 10.176l-4.928 4.928-4.928-4.928-0.896 0.896 4.928 4.928-4.928 4.928 0.896 0.896 4.928-4.928 4.928 4.928 0.896-0.896-4.928-4.928 4.928-4.928-0.896-0.896zM16 2.080q-3.776 0-7.040 1.888-3.136 1.856-4.992 4.992-1.888 3.264-1.888 7.040t1.888 7.040q1.856 3.136 4.992 4.992 3.264 1.888 7.040 1.888t7.040-1.888q3.136-1.856 4.992-4.992 1.888-3.264 1.888-7.040t-1.888-7.040q-1.856-3.136-4.992-4.992-3.264-1.888-7.040-1.888zM16 28.64q-3.424 0-6.4-1.728-2.848-1.664-4.512-4.512-1.728-2.976-1.728-6.4t1.728-6.4q1.664-2.848 4.512-4.512 2.976-1.728 6.4-1.728t6.4 1.728q2.848 1.664 4.512 4.512 1.728 2.976 1.728 6.4t-1.728 6.4q-1.664 2.848-4.512 4.512-2.976 1.728-6.4 1.728z",c:"#f43530"},download:{d:"M15.808 1.696q-3.776 0-7.072 1.984-3.2 1.888-5.088 5.152-1.952 3.392-1.952 7.36 0 3.776 1.952 7.072 1.888 3.2 5.088 5.088 3.296 1.952 7.072 1.952 3.968 0 7.36-1.952 3.264-1.888 5.152-5.088 1.984-3.296 1.984-7.072 0-4-1.984-7.36-1.888-3.264-5.152-5.152-3.36-1.984-7.36-1.984zM20.864 18.592l-3.776 4.928q-0.448 0.576-1.088 0.576t-1.088-0.576l-3.776-4.928q-0.448-0.576-0.24-0.992t0.944-0.416h2.976v-8.928q0-0.256 0.176-0.432t0.4-0.176h1.216q0.224 0 0.4 0.176t0.176 0.432v8.928h2.976q0.736 0 0.944 0.416t-0.24 0.992z",c:er},search:{d:"M20.928 22.688q-1.696 1.376-3.744 2.112-2.112 0.768-4.384 0.768-3.488 0-6.464-1.728-2.88-1.696-4.576-4.608-1.76-2.976-1.76-6.464t1.76-6.464q1.696-2.88 4.576-4.576 2.976-1.76 6.464-1.76t6.464 1.76q2.912 1.696 4.608 4.576 1.728 2.976 1.728 6.464 0 2.272-0.768 4.384-0.736 2.048-2.112 3.744l9.312 9.28-1.824 1.824-9.28-9.312zM12.8 23.008q2.784 0 5.184-1.376 2.304-1.376 3.68-3.68 1.376-2.4 1.376-5.184t-1.376-5.152q-1.376-2.336-3.68-3.68-2.4-1.408-5.184-1.408t-5.152 1.408q-2.336 1.344-3.68 3.68-1.408 2.368-1.408 5.152t1.408 5.184q1.344 2.304 3.68 3.68 2.368 1.376 5.152 1.376zM12.8 23.008v0z",c:ag},clear:{d:"M16 0q-4.352 0-8.064 2.176-3.616 2.144-5.76 5.76-2.176 3.712-2.176 8.064t2.176 8.064q2.144 3.616 5.76 5.76 3.712 2.176 8.064 2.176t8.064-2.176q3.616-2.144 5.76-5.76 2.176-3.712 2.176-8.064t-2.176-8.064q-2.144-3.616-5.76-5.76-3.712-2.176-8.064-2.176zM22.688 21.408q0.32 0.32 0.304 0.752t-0.336 0.736-0.752 0.304-0.752-0.32l-5.184-5.376-5.376 5.184q-0.32 0.32-0.752 0.304t-0.736-0.336-0.304-0.752 0.32-0.752l5.376-5.184-5.184-5.376q-0.32-0.32-0.304-0.752t0.336-0.752 0.752-0.304 0.752 0.336l5.184 5.376 5.376-5.184q0.32-0.32 0.752-0.304t0.752 0.336 0.304 0.752-0.336 0.752l-5.376 5.184 5.184 5.376z",c:ag}};const sg=qp({name:"Icon",props:{type:{type:String,required:!0,default:""},size:{type:[String,Number],default:23},color:{type:String,default:""}},setup(e){var t=Qa(null),n=Fl((()=>og[e.type]));return()=>{var{value:r}=n;return vl("uni-icon",{ref:t},[r&&r.d&&Au(r.d,e.color||r.c,Mu(e.size))],512)}}});var lg={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},ug={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},cg={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]};const dg=qp({name:"Image",props:lg,setup(e,t){var{emit:n}=t,r=Qa(null),i=function(e,t){var n=Qa(""),r=Fl((()=>{var e="auto",r="",i=cg[t.mode];return i?(i[0]&&(r=i[0]),i[1]&&(e=i[1])):(r="0% 0%",e="100% 100%"),"background-image:".concat(n.value?'url("'+n.value+'")':"none",";background-position:").concat(r,";background-size:").concat(e,";")})),i=za({rootEl:e,src:Fl((()=>t.src?mc(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:r,imgSrc:n});return rs((()=>{var t=e.value;i.origWidth=t.clientWidth||0,i.origHeight=t.clientHeight||0})),i}(r,e),a=Zp(r,n),{fixSize:o}=function(e,t,n){var r=()=>{var{mode:r}=t,i=ug[r];if(i){var{origWidth:a,origHeight:o}=n,s=a&&o?a/o:0;if(s){var l=e.value,u=l[i[0]];u&&(l.style[i[1]]=function(e){hg&&e>10&&(e=2*Math.round(e/2));return e}(i[2](u,s))+"px"),window.dispatchEvent(new CustomEvent("updateview"))}}},i=()=>{var{style:t}=e.value,{origStyle:{width:r,height:i}}=n;t.width=r,t.height=i};return $o((()=>t.mode),((e,t)=>{ug[t]&&i(),ug[e]&&r()})),{fixSize:r,resetSize:i}}(r,e,i);return function(e,t,n,r,i){var a,o,s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";e.origWidth=t,e.origHeight=n,e.imgSrc=r},l=l=>{if(!l)return u(),void s();(a=a||new Image).onload=e=>{var{width:c,height:d}=a;s(c,d,l),_o((()=>{r()})),a.draggable=t.draggable,o&&o.remove(),o=a,n.value.appendChild(a),u(),i("load",e,{width:c,height:d})},a.onerror=t=>{s(),u(),i("error",t,{errMsg:"GET ".concat(e.src," 404 (Not Found)")})},a.src=l},u=()=>{a&&(a.onload=null,a.onerror=null,a=null)};$o((()=>e.src),(e=>l(e))),$o((()=>e.imgSrc),(e=>{!e&&o&&(o.remove(),o=null)})),rs((()=>l(e.src))),os((()=>u()))}(i,e,r,o,a),()=>vl("uni-image",{ref:r},[vl("div",{style:i.modeStyle},null,4),ug[e.mode]?vl(sv,{onResize:o},null,8,["onResize"]):vl("span",null,null)],512)}});var hg="Google Inc."===navigator.vendor;var fg=yr(!0),pg=[],vg=0,gg=!1,mg=e=>pg.forEach((t=>t.userAction=e));function _g(){var e=za({userAction:!1});return rs((()=>{!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{userAction:!1};gg||(["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!vg&&mg(!0),vg++,setTimeout((()=>{! --vg&&mg(!1)}),0)}),fg)})),gg=!0);pg.push(e)}(e)})),os((()=>{var t,n;t=e,(n=pg.indexOf(t))>=0&&pg.splice(n,1)})),{state:e}}function yg(){var e=za({attrs:{}});return rs((()=>{for(var t=Ol();t;){var n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function bg(e,t){var n=document.activeElement;if(!n)return t({});var r={};["input","textarea"].includes(n.tagName.toLowerCase())&&(r.start=n.selectionStart,r.end=n.selectionEnd),t(r)}var wg,xg=200;function Sg(e,t,n){"number"===t&&isNaN(Number(e))&&(e="");var r=null==e?"":String(e);return null==n?r:r.slice(0,n)}var kg=["none","text","decimal","numeric","tel","search","email","url"],Tg=on({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~kg.indexOf(e)},cursorColor:{type:String,default:""}},Mv),Eg=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend",...Lv];function Cg(e,t,n,r){var i=null;i=function(e,t,n){var r,{clearTimeout:i,setTimeout:a}=n,o=function(){i(r),r=a((()=>e.apply(this,arguments)),t)};return o.cancel=function(){i(r)},o}((n=>{t.value=Sg(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout}),$o((()=>e.modelValue),i),$o((()=>e.value),i);var a=function(e,t){var n,r,i=0,a=function(){for(var a=arguments.length,o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];var l=Date.now();clearTimeout(n),r=()=>{r=null,i=l,e.apply(this,o)},l-i<t?n=setTimeout(r,t-(l-i)):r()};return a.cancel=function(){clearTimeout(n),r=null},a.flush=function(){clearTimeout(n),r&&r()},a}(((e,t)=>{i.cancel(),n("update:modelValue",t.value),n("update:value",t.value),r("input",e,t)}),100);return ns((()=>{i.cancel(),a.cancel()})),{trigger:r,triggerInput:(e,t,n)=>{i.cancel(),a(e,t),n&&a.flush()}}}function Og(e,t){var{state:n}=_g(),r=Fl((()=>e.autoFocus||e.focus));function i(){if(r.value){var e=t.value;if(e&&"plus"in window){var a=xg-(Date.now()-wg);a>0?setTimeout(i,a):(e.focus(),n.userAction||plus.key.showSoftKeybord())}else setTimeout(i,100)}}$o((()=>e.focus),(e=>{var n;e?i():(n=t.value)&&n.blur()})),rs((()=>{wg=wg||Date.now(),r.value&&_o(i)}))}function Mg(e,t,n,r){_i(Nu(),"getSelectedTextRange",bg);var{fieldRef:i,state:a,trigger:o}=function(e,t,n){var r,i=Qa(null),a=Zp(t,n),o=Fl((()=>{var t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=Fl((()=>{var t=Number(e.selectionEnd);return isNaN(t)?-1:t})),l=Fl((()=>{var t=Number(e.cursor);return isNaN(t)?-1:t})),u=Fl((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),c=za({value:r=Sg(e.modelValue,e.type)||Sg(e.value,e.type),valueOrigin:r,maxlength:u,focus:e.focus,composing:!1,selectionStart:o,selectionEnd:s,cursor:l});return $o((()=>c.focus),(e=>n("update:focus",e))),$o((()=>c.maxlength),(e=>c.value=c.value.slice(0,e)),{immediate:!1}),{fieldRef:i,state:c,trigger:a}}(e,t,n),{triggerInput:s}=Cg(e,a,n,o);Og(e,i),Iv(e,i,o);var{state:l}=yg();return function(e,t){var n=Ns(Jp,!1);if(n){var r=Ol(),i={submit(){var n=r.proxy;return[n[e],pn(t)?n[t]:t.value]},reset(){pn(t)?r.proxy[t]="":t.value=""}};n.addField(i),os((()=>{n.removeField(i)}))}}("name",a),function(e,t,n,r,i,a){function o(){var n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function s(){var n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}$o([()=>t.selectionStart,()=>t.selectionEnd],o),$o((()=>t.cursor),s),$o((()=>e.value),(function(){var u=e.value;if(u){var c=function(e,r){e.stopPropagation(),fn(a)&&!1===a(e,t)||(t.value=u.value,t.composing&&n.ignoreCompositionEvent||i(e,{value:u.value,cursor:l(u)},r))};u.addEventListener("change",(e=>e.stopPropagation())),u.addEventListener("focus",(function(e){t.focus=!0,r("focus",e,{value:t.value}),o(),s()})),u.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,c(e,!0)),t.focus=!1,r("blur",e,{value:t.value,cursor:l(e.target)})})),u.addEventListener("input",c),u.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),u.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,c(e)),d(e)})),u.addEventListener("compositionupdate",d)}function d(e){n.ignoreCompositionEvent||r(e.type,e,{value:e.data})}}))}(i,a,e,o,s,r),{fieldRef:i,state:a,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:o}}function Lg(e,t,n,r,i){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=r.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",i&&(i.fn=()=>{n.value=r.value=t.value=t.value.slice(0,-1),r.removeEventListener("blur",i.fn)},r.addEventListener("blur",i.fn)),!1}else if("deleteContentBackward"===e.inputType&&"iOS"===plus.os.name&&plus.os.version&&16===parseInt(plus.os.version)&&"."===t.value.slice(-2,-1))return t.value=n.value=r.value=t.value.slice(0,-2),!0}const Ig=qp({name:"Input",props:on({},Tg,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...Eg],setup(e,t){var{emit:n,expose:r}=t,i=["text","number","idcard","digit","password","tel"],a=["off","one-time-code"],o=Fl((()=>{var t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~i.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=Fl((()=>{var t=a.indexOf(e.textContentType),n=a.indexOf(On(e.textContentType));return a[-1!==t?t:-1!==n?n:0]})),l=function(e,t){if("number"===t.value){var n=void 0===e.modelValue?e.value:e.modelValue,r=Qa(null!=n?n.toLocaleString():"");return $o((()=>e.modelValue),(e=>{r.value=null!=e?e.toLocaleString():""})),$o((()=>e.value),(e=>{r.value=null!=e?e.toLocaleString():""})),r}return Qa("")}(e,o),u={fn:null},c=Qa(null),{fieldRef:d,state:h,scopedAttrsState:f,fixDisabledColor:p,trigger:v}=Mg(e,c,n,((e,t)=>{var n=e.target;if("number"===o.value){if(u.fn&&(n.removeEventListener("blur",u.fn),u.fn=null),n.validity&&!n.validity.valid){if((!l.value||!n.value)&&"-"===e.data||"-"===l.value[0]&&"deleteContentBackward"===e.inputType)return l.value="-",t.value="",u.fn=()=>{l.value=n.value=""},n.addEventListener("blur",u.fn),!1;var r=Lg(e,l,t,n,u);return"boolean"==typeof r?r:(l.value=t.value=n.value="-"===l.value?"":l.value,!1)}var i=Lg(e,l,t,n,u);if("boolean"==typeof i)return i;l.value=n.value;var a=t.maxlength;if(a>0&&n.value.length>a)return n.value=n.value.slice(0,a),t.value=n.value,!1}}));$o((()=>h.value),(t=>{"number"!==e.type||"-"===l.value&&""===t||(l.value=t.toString())}));var g=["number","digit"],m=Fl((()=>g.includes(e.type)?e.step:""));function _(t){if("Enter"===t.key){var n=t.target;t.stopPropagation(),v("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}}return r({$triggerInput:e=>{n("update:modelValue",e.value),n("update:value",e.value),h.value=e.value}}),()=>{var t=e.disabled&&p?vl("input",{key:"disabled-input",ref:d,value:h.value,tabindex:"-1",readonly:!!e.disabled,type:o.value,maxlength:h.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):vl("input",{key:"input",ref:d,value:h.value,onInput:e=>{h.value=e.target.value.toString()},disabled:!!e.disabled,type:o.value,maxlength:h.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:_,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return vl("uni-input",{ref:c},[vl("div",{class:"uni-input-wrapper"},[Uo(vl("div",wl(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Xl,!(h.value.length||"-"===l.value||l.value.includes("."))]]),"search"===e.confirmType?vl("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});var Ag,Bg,Ng=["class","style"],Rg=/^on[A-Z]+/,Pg=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{excludeListeners:n=!1,excludeKeys:r=[]}=t,i=Ol(),a=eo({}),o=eo({}),s=eo({}),l=r.concat(Ng);return i.attrs=za(i.attrs),jo((()=>{var e,t=(e=i.attrs,Object.keys(e).map((t=>[t,e[t]]))).reduce(((e,t)=>{var[r,i]=t;return l.includes(r)?e.exclude[r]=i:Rg.test(r)?(n||(e.attrs[r]=i),e.listeners[r]=i):e.attrs[r]=i,e}),{exclude:{},attrs:{},listeners:{}});a.value=t.attrs,o.value=t.listeners,s.value=t.exclude}),null,e),{$attrs:a,$listeners:o,$excludeAttrs:s}};function Dg(){wr((()=>{Ag||(Ag=plus.webview.currentWebview()),Bg||(Bg=(Ag.getStyle()||{}).pullToRefresh||{})}))}function zg(e){var{disable:t}=e;Bg&&Bg.support&&Ag.setPullToRefresh(Object.assign({},Bg,{support:!t}))}function Fg(e){var t=[];return cn(e)&&e.forEach((e=>{cl(e)?e.type===rl?t.push(...Fg(e.children)):t.push(e):cn(e)&&t.push(...Fg(e))})),t}function $g(e){Ol().rebuild=e}const jg=qp({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,t){var{slots:n}=t,r=Qa(null),i=Qa(!1),{setContexts:a,events:o}=function(e,t){var n=Qa(0),r=Qa(0),i=za({x:null,y:null}),a=Qa(null),o=null,s=[];function l(t){t&&1!==t&&(e.scaleArea?s.forEach((function(e){e._setScale(t)})):o&&o._setScale(t))}function u(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,r=t.value;function i(e){for(var t=0;t<n.length;t++){var a=n[t];if(e===a.rootRef.value)return a}return e===r||e===document.body||e===document?null:i(e.parentNode)}return i(e)}var c=Xp((t=>{zg({disable:!0});var n=t.touches;if(n&&n.length>1){var r={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(a.value=Vg(r),i.x=r.x,i.y=r.y,!e.scaleArea){var s=u(n[0].target),l=u(n[1].target);o=s&&s===l?s:null}}})),d=Xp((e=>{var t=e.touches;if(t&&t.length>1){e.preventDefault();var n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==i.x&&a.value&&a.value>0)l(Vg(n)/a.value);i.x=n.x,i.y=n.y}})),h=Xp((t=>{zg({disable:!1});var n=t.touches;n&&n.length||t.changedTouches&&(i.x=0,i.y=0,a.value=null,e.scaleArea?s.forEach((function(e){e._endScale()})):o&&o._endScale())}));function f(){p(),s.forEach((function(e,t){e.setParent()}))}function p(){var e=window.getComputedStyle(t.value),i=t.value.getBoundingClientRect();n.value=i.width-["Left","Right"].reduce((function(t,n){var r="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[r])}),0),r.value=i.height-["Top","Bottom"].reduce((function(t,n){var r="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[r])}),0)}return Bs("movableAreaWidth",n),Bs("movableAreaHeight",r),{setContexts(e){s=e},events:{_onTouchstart:c,_onTouchmove:d,_onTouchend:h,_resize:f}}}(e,r),{$listeners:s,$attrs:l,$excludeAttrs:u}=Pg(),c=s.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{var t=c[e],n=o["_".concat(e)];c[e]=t?[].concat(t,n):n})),rs((()=>{o._resize(),Dg(),i.value=!0}));var d=[],h=[];function f(){for(var e=[],t=function(t){var n=d[t];n instanceof Element||(n=n.el);var r=h.find((e=>n===e.rootRef.value));r&&e.push(qa(r))},n=0;n<d.length;n++)t(n);a(e)}$g((()=>{d=r.value.children,f()}));return Bs("_isMounted",i),Bs("movableAreaRootRef",r),Bs("addMovableViewContext",(e=>{h.push(e),f()})),Bs("removeMovableViewContext",(e=>{var t=h.indexOf(e);t>=0&&(h.splice(t,1),f())})),()=>(n.default&&n.default(),vl("uni-movable-area",wl({ref:r},l.value,u.value,c),[vl(sv,{onResize:o._resize},null,8,["onResize"]),d],16))}});function Vg(e){return Math.sqrt(e.x*e.x+e.y*e.y)}var Wg,Hg,Ug=function(e,t,n,r){e.addEventListener(t,(e=>{fn(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};function qg(e,t,n){os((()=>{document.removeEventListener("mousemove",Wg),document.removeEventListener("mouseup",Hg)}));var r,i,a=0,o=0,s=0,l=0,u=function(e,n,r,i){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:r,y:i,dx:r-a,dy:i-o,ddx:r-s,ddy:i-l,timeStamp:e.timeStamp}}))return!1},c=null;Ug(e,"touchstart",(function(e){if(r=!0,1===e.touches.length&&!c)return c=e,a=s=e.touches[0].pageX,o=l=e.touches[0].pageY,u(e,"start",a,o)})),Ug(e,"mousedown",(function(e){if(i=!0,!r&&!c)return c=e,a=s=e.pageX,o=l=e.pageY,u(e,"start",a,o)})),Ug(e,"touchmove",(function(e){if(1===e.touches.length&&c){var t=u(e,"move",e.touches[0].pageX,e.touches[0].pageY);return s=e.touches[0].pageX,l=e.touches[0].pageY,t}}));var d=Wg=function(e){if(!r&&i&&c){var t=u(e,"move",e.pageX,e.pageY);return s=e.pageX,l=e.pageY,t}};document.addEventListener("mousemove",d),Ug(e,"touchend",(function(e){if(0===e.touches.length&&c)return r=!1,c=null,u(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));var h=Hg=function(e){if(i=!1,!r&&c)return c=null,u(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",h),Ug(e,"touchcancel",(function(e){if(c){r=!1;var t=c;return c=null,u(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function Yg(e,t,n){return e>t-n&&e<t+n}function Xg(e,t){return Yg(e,0,t)}function Zg(){}function Gg(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function Kg(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function Jg(e,t,n){this._springX=new Kg(e,t,n),this._springY=new Kg(e,t,n),this._springScale=new Kg(e,t,n),this._startTime=0}function Qg(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}Zg.prototype.x=function(e){return Math.sqrt(e)},Gg.prototype.setV=function(e,t){var n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},Gg.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},Gg.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);var t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},Gg.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},Gg.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},Gg.prototype.dt=function(){return-this._x_v/this._x_a},Gg.prototype.done=function(){var e=Yg(this.s().x,this._endPositionX)||Yg(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},Gg.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},Gg.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},Kg.prototype._solve=function(e,t){var n=this._c,r=this._m,i=this._k,a=n*n-4*r*i;if(0===a){var o=-n/(2*r),s=e,l=t/(o*e);return{x:function(e){return(s+l*e)*Math.pow(Math.E,o*e)},dx:function(e){var t=Math.pow(Math.E,o*e);return o*(s+l*e)*t+l*t}}}if(a>0){var u=(-n-Math.sqrt(a))/(2*r),c=(-n+Math.sqrt(a))/(2*r),d=(t-u*e)/(c-u),h=e-d;return{x:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*t+d*n},dx:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*u*t+d*c*n}}}var f=Math.sqrt(4*r*i-n*n)/(2*r),p=-n/2*r,v=e,g=(t-p*e)/f;return{x:function(e){return Math.pow(Math.E,p*e)*(v*Math.cos(f*e)+g*Math.sin(f*e))},dx:function(e){var t=Math.pow(Math.E,p*e),n=Math.cos(f*e),r=Math.sin(f*e);return t*(g*f*n-v*f*r)+p*t*(g*r+v*n)}}},Kg.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},Kg.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},Kg.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Xg(t,.1)){t=t||0;var r=this._endPosition;this._solution&&(Xg(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),r=this._solution.x((n-this._startTime)/1e3),Xg(t,.1)&&(t=0),Xg(r,.1)&&(r=0),r+=this._endPosition),this._solution&&Xg(r-e,.1)&&Xg(t,.1)||(this._endPosition=e,this._solution=this._solve(r-this._endPosition,t),this._startTime=n)}},Kg.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},Kg.prototype.done=function(e){return e||(e=(new Date).getTime()),Yg(this.x(),this._endPosition,.1)&&Xg(this.dx(),.1)},Kg.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},Kg.prototype.springConstant=function(){return this._k},Kg.prototype.damping=function(){return this._c},Kg.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},Jg.prototype.setEnd=function(e,t,n,r){var i=(new Date).getTime();this._springX.setEnd(e,r,i),this._springY.setEnd(t,r,i),this._springScale.setEnd(n,r,i),this._startTime=i},Jg.prototype.x=function(){var e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},Jg.prototype.done=function(){var e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},Jg.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};const em=qp({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,t){var{slots:n,emit:r}=t,i=Qa(null),a=Zp(i,r),{setParent:o}=function(e,t,n){var r,i,a=Ns("_isMounted",Qa(!1)),o=Ns("addMovableViewContext",(()=>{})),s=Ns("removeMovableViewContext",(()=>{})),l=Qa(1),u=Qa(1),c=Qa(!1),d=Qa(0),h=Qa(0),f=null,p=null,v=!1,g=null,m=null,_=new Zg,y=new Zg,b={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=Fl((()=>{var t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new Gg(1,w.value);$o((()=>e.disabled),(()=>{U()}));var{_updateOldScale:S,_endScale:k,_setScale:T,scaleValueSync:E,_updateBoundary:C,_updateOffset:O,_updateWH:M,_scaleOffset:L,minX:I,minY:A,maxX:B,maxY:N,FAandSFACancel:R,_getLimitXY:P,_setTransform:D,_revise:z,dampingNumber:F,xMove:$,yMove:j,xSync:V,ySync:W,_STD:H}=function(e,t,n,r,i,a,o,s,l,u){var c=Fl((()=>{var t=Number(e.scaleMin);return isNaN(t)?.5:t})),d=Fl((()=>{var t=Number(e.scaleMax);return isNaN(t)?10:t})),h=Qa(Number(e.scaleValue)||1);$o(h,(e=>{D(e)})),$o(c,(()=>{P()})),$o(d,(()=>{P()})),$o((()=>e.scaleValue),(e=>{h.value=Number(e)||0}));var{_updateBoundary:f,_updateOffset:p,_updateWH:v,_scaleOffset:g,minX:m,minY:_,maxX:y,maxY:b}=function(e,t,n){var r=Ns("movableAreaWidth",Qa(0)),i=Ns("movableAreaHeight",Qa(0)),a=Ns("movableAreaRootRef"),o={x:0,y:0},s={x:0,y:0},l=Qa(0),u=Qa(0),c=Qa(0),d=Qa(0),h=Qa(0),f=Qa(0);function p(){var e=0-o.x+s.x,t=r.value-l.value-o.x-s.x;c.value=Math.min(e,t),h.value=Math.max(e,t);var n=0-o.y+s.y,a=i.value-u.value-o.y-s.y;d.value=Math.min(n,a),f.value=Math.max(n,a)}function v(){o.x=rm(e.value,a.value),o.y=im(e.value,a.value)}function g(r){r=r||t.value,r=n(r);var i=e.value.getBoundingClientRect();u.value=i.height/t.value,l.value=i.width/t.value;var a=u.value*r,o=l.value*r;s.x=(o-l.value)/2,s.y=(a-u.value)/2}return{_updateBoundary:p,_updateOffset:v,_updateWH:g,_scaleOffset:s,minX:c,minY:d,maxX:h,maxY:f}}(t,r,R),{FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:k,_revise:T,dampingNumber:E,xMove:C,yMove:O,xSync:M,ySync:L,_STD:I}=function(e,t,n,r,i,a,o,s,l,u,c,d,h,f){var p=Fl((()=>{var e=Number(t.damping);return isNaN(e)?20:e})),v=Fl((()=>"all"===t.direction||"horizontal"===t.direction)),g=Fl((()=>"all"===t.direction||"vertical"===t.direction)),m=Qa(om(t.x)),_=Qa(om(t.y));$o((()=>t.x),(e=>{m.value=om(e)})),$o((()=>t.y),(e=>{_.value=om(e)})),$o(m,(e=>{T(e)})),$o(_,(e=>{E(e)}));var y=new Jg(1,9*Math.pow(p.value,2)/40,p.value);function b(e,t){var n=!1;return e>i.value?(e=i.value,n=!0):e<o.value&&(e=o.value,n=!0),t>a.value?(t=a.value,n=!0):t<s.value&&(t=s.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){d&&d.cancel(),c&&c.cancel()}function x(e,n,i,a,o,s){w(),v.value||(e=l.value),g.value||(n=u.value),t.scale||(i=r.value);var d=b(e,n);e=d.x,n=d.y,t.animation?(y._springX._solution=null,y._springY._solution=null,y._springScale._solution=null,y._springX._endPosition=l.value,y._springY._endPosition=u.value,y._springScale._endPosition=r.value,y.setEnd(e,n,i,1),c=am(y,(function(){var e=y.x();S(e.x,e.y,e.scale,a,o,s)}),(function(){c.cancel()}))):S(e,n,i,a,o,s)}function S(i,a,o){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",c=arguments.length>4?arguments[4]:void 0,d=arguments.length>5?arguments[5]:void 0;null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=l.value||0),null!==a&&"NaN"!==a.toString()&&"number"==typeof a||(a=u.value||0),i=Number(i.toFixed(1)),a=Number(a.toFixed(1)),o=Number(o.toFixed(1)),l.value===i&&u.value===a||c||f("change",{},{x:Qg(i,n.x),y:Qg(a,n.y),source:s}),t.scale||(o=r.value),o=+(o=h(o)).toFixed(3),d&&o!==r.value&&f("scale",{},{x:i,y:a,scale:o});var p="translateX("+i+"px) translateY("+a+"px) translateZ(0px) scale("+o+")";e.value&&(e.value.style.transform=p,e.value.style.webkitTransform=p,l.value=i,u.value=a,r.value=o)}function k(e){var t=b(l.value,u.value),n=t.x,i=t.y,a=t.outOfBounds;return a&&x(n,i,r.value,e),a}function T(e){if(v.value){if(e+n.x===l.value)return l;c&&c.cancel(),x(e+n.x,_.value+n.y,r.value)}return e}function E(e){if(g.value){if(e+n.y===u.value)return u;c&&c.cancel(),x(m.value+n.x,e+n.y,r.value)}return e}return{FAandSFACancel:w,_getLimitXY:b,_animationTo:x,_setTransform:S,_revise:k,dampingNumber:p,xMove:v,yMove:g,xSync:m,ySync:_,_STD:y}}(t,e,g,r,y,b,m,_,o,s,l,u,R,n);function A(t,n){if(e.scale){t=R(t),v(t),f();var r=x(o.value,s.value),i=r.x,a=r.y;n?S(i,a,t,"",!0,!0):nm((function(){k(i,a,t,"",!0,!0)}))}}function B(){a.value=!0}function N(e){i.value=e}function R(e){return e=Math.max(.5,c.value,e),e=Math.min(10,d.value,e)}function P(){if(!e.scale)return!1;A(r.value,!0),N(r.value)}function D(t){return!!e.scale&&(A(t=R(t),!0),N(t),t)}function z(){a.value=!1,N(r.value)}function F(e){e&&(e=i.value*e,B(),A(e))}return{_updateOldScale:N,_endScale:z,_setScale:F,scaleValueSync:h,_updateBoundary:f,_updateOffset:p,_updateWH:v,_scaleOffset:g,minX:m,minY:_,maxX:y,maxY:b,FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:k,_revise:T,dampingNumber:E,xMove:C,yMove:O,xSync:M,ySync:L,_STD:I}}(e,n,t,l,u,c,d,h,f,p);function U(){c.value||e.disabled||(zg({disable:!0}),R(),b.historyX=[0,0],b.historyY=[0,0],b.historyT=[0,0],$.value&&(r=d.value),j.value&&(i=h.value),n.value.style.willChange="transform",g=null,m=null,v=!0)}function q(t){if(!c.value&&!e.disabled&&v){var n=d.value,a=h.value;if(null===m&&(m=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),$.value&&(n=t.detail.dx+r,b.historyX.shift(),b.historyX.push(n),j.value||null!==g||(g=Math.abs(t.detail.dx/t.detail.dy)<1)),j.value&&(a=t.detail.dy+i,b.historyY.shift(),b.historyY.push(a),$.value||null!==g||(g=Math.abs(t.detail.dy/t.detail.dx)<1)),b.historyT.shift(),b.historyT.push(t.detail.timeStamp),!g){t.preventDefault();var o="touch";n<I.value?e.outOfBounds?(o="touch-out-of-bounds",n=I.value-_.x(I.value-n)):n=I.value:n>B.value&&(e.outOfBounds?(o="touch-out-of-bounds",n=B.value+_.x(n-B.value)):n=B.value),a<A.value?e.outOfBounds?(o="touch-out-of-bounds",a=A.value-y.x(A.value-a)):a=A.value:a>N.value&&(e.outOfBounds?(o="touch-out-of-bounds",a=N.value+y.x(a-N.value)):a=N.value),nm((function(){D(n,a,l.value,o)}))}}}function Y(){if(!c.value&&!e.disabled&&v&&(zg({disable:!1}),n.value.style.willChange="auto",v=!1,!g&&!z("out-of-bounds")&&e.inertia)){var t=1e3*(b.historyX[1]-b.historyX[0])/(b.historyT[1]-b.historyT[0]),r=1e3*(b.historyY[1]-b.historyY[0])/(b.historyT[1]-b.historyT[0]),i=d.value,a=h.value;x.setV(t,r),x.setS(i,a);var o=x.delta().x,s=x.delta().y,u=o+i,f=s+a;u<I.value?(u=I.value,f=a+(I.value-i)*s/o):u>B.value&&(u=B.value,f=a+(B.value-i)*s/o),f<A.value?(f=A.value,u=i+(A.value-a)*o/s):f>N.value&&(f=N.value,u=i+(N.value-a)*o/s),x.setEnd(u,f),p=am(x,(function(){var e=x.s(),t=e.x,n=e.y;D(t,n,l.value,"friction")}),(function(){p.cancel()}))}e.outOfBounds||e.inertia||R()}function X(){if(a.value){R();var t=e.scale?E.value:1;O(),M(t),C();var n=P(V.value+L.x,W.value+L.y),r=n.x,i=n.y;D(r,i,t,"",!0),S(t)}}return rs((()=>{qg(n.value,(e=>{switch(e.detail.state){case"start":U();break;case"move":q(e);break;case"end":Y()}})),X(),x.reconfigure(1,w.value),H.reconfigure(1,9*Math.pow(F.value,2)/40,F.value),n.value.style.transformOrigin="center",Dg();var e={rootRef:n,setParent:X,_endScale:k,_setScale:T};o(e),ss((()=>{s(e)}))})),ss((()=>{R()})),{setParent:X}}(e,a,i);return()=>vl("uni-movable-view",{ref:i},[vl(sv,{onResize:o},null,8,["onResize"]),n.default&&n.default()],512)}});var tm=!1;function nm(e){tm||(tm=!0,requestAnimationFrame((function(){e(),tm=!1})))}function rm(e,t){if(e===t)return 0;var n=e.offsetLeft;return e.offsetParent?n+=rm(e.offsetParent,t):0}function im(e,t){if(e===t)return 0;var n=e.offsetTop;return e.offsetParent?n+=im(e.offsetParent,t):0}function am(e,t,n){var r={id:0,cancelled:!1};return function e(t,n,r,i){if(!t||!t.cancelled){r(n);var a=n.done();a||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,r,i))),a&&i&&i(n)}}(r,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,r),model:e}}function om(e){return/\d+[ur]px$/i.test(e)?uni.upx2px(parseFloat(e)):Number(e)||0}var sm=["navigate","redirect","switchTab","reLaunch","navigateBack"],lm=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],um=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],cm={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~sm.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||lm.concat(um).includes(e)},animationDuration:{type:[String,Number],default:300}};const dm=qp({name:"Navigator",inheritAttrs:!1,compatConfig:{MODE:3},props:on({},cm,{renderLink:{type:Boolean,default:!0}}),setup(e,t){var{slots:n}=t,r=Qa(null),i=Ol(),a=i&&i.vnode.scopeId||"",{hovering:o,binding:s}=Gp(e),l=function(e){return()=>{if("navigateBack"===e.openType||e.url){var t=parseInt(e.animationDuration);switch(e.openType){case"navigate":uni.navigateTo({url:e.url,animationType:e.animationType||"pop-in",animationDuration:t});break;case"redirect":uni.redirectTo({url:e.url,exists:e.exists});break;case"switchTab":uni.switchTab({url:e.url});break;case"reLaunch":uni.reLaunch({url:e.url});break;case"navigateBack":uni.navigateBack({delta:e.delta,animationType:e.animationType||"pop-out",animationDuration:t})}}else console.error("<navigator/> should have url attribute when using navigateTo, redirectTo, reLaunch or switchTab")}}(e);return()=>{var{hoverClass:t,url:u}=e,c=e.hoverClass&&"none"!==e.hoverClass,d=e.renderLink?vl("a",{class:"navigator-wrap",href:u,onClick:ku,onMousedown:ku},[n.default&&n.default()],40,["href","onClick","onMousedown"]):n.default&&n.default();return vl("uni-navigator",wl({class:c&&o.value?t:"",ref:r},c&&s,i?i.attrs:{},{[a]:""},{onClick:l}),[d],16,["onClick"])}}});const hm=qp({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return cn(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,t){var{slots:n,emit:r}=t,i=Qa(null),a=Qa(null),o=Zp(i,r),s=function(e){var t=za([...e.value]),n=za({value:t,height:34});return $o((()=>e.value),((e,t)=>{(e===t||e.length!==t.length||e.findIndex(((e,n)=>e!==t[n]))>=0)&&(n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)})))})),n}(e),l=Qa(null),u=Qa([]),c=Qa([]);function d(e){var t=c.value;if(t instanceof HTMLCollection)return Array.prototype.indexOf.call(t,e.el);var n=(t=t.filter((e=>e.type!==al))).indexOf(e);return-1!==n?n:u.value.indexOf(e)}return Bs("getPickerViewColumn",(function(e){return Fl({get(){var t=d(e.vnode);return s.value[t]||0},set(t){var n=d(e.vnode);if(!(n<0)&&s.value[n]!==t){s.value[n]=t;var i=s.value.map((e=>e));r("update:value",i),o("change",{},{value:i})}}})})),Bs("pickerViewProps",e),Bs("pickerViewState",s),$g((()=>{var e;(e=l.value)&&(s.height=e.$el.offsetHeight),a.value&&(c.value=a.value.children)})),()=>{var e=n.default&&n.default();return vl("uni-picker-view",{ref:i},[vl(sv,{ref:l,onResize:e=>{var{height:t}=e;return s.height=t}},null,8,["onResize"]),vl("div",{ref:a,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class fm{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);var t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);var t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){var t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){var e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function pm(e,t,n){return e>t-n&&e<t+n}function vm(e,t){return pm(e,0,t)}class gm{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){var n=this._c,r=this._m,i=this._k,a=n*n-4*r*i;if(0===a){var o=-n/(2*r),s=e,l=t/(o*e);return{x:function(e){return(s+l*e)*Math.pow(Math.E,o*e)},dx:function(e){var t=Math.pow(Math.E,o*e);return o*(s+l*e)*t+l*t}}}if(a>0){var u=(-n-Math.sqrt(a))/(2*r),c=(-n+Math.sqrt(a))/(2*r),d=(t-u*e)/(c-u),h=e-d;return{x:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*t+d*n},dx:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*u*t+d*c*n}}}var f=Math.sqrt(4*r*i-n*n)/(2*r),p=-n/2*r,v=e,g=(t-p*e)/f;return{x:function(e){return Math.pow(Math.E,p*e)*(v*Math.cos(f*e)+g*Math.sin(f*e))},dx:function(e){var t=Math.pow(Math.E,p*e),n=Math.cos(f*e),r=Math.sin(f*e);return t*(g*f*n-v*f*r)+p*t*(g*r+v*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!vm(t,.4)){t=t||0;var r=this._endPosition;this._solution&&(vm(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),r=this._solution.x((n-this._startTime)/1e3),vm(t,.4)&&(t=0),vm(r,.4)&&(r=0),r+=this._endPosition),this._solution&&vm(r-e,.4)&&vm(t,.4)||(this._endPosition=e,this._solution=this._solve(r-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),pm(this.x(),this._endPosition,.4)&&vm(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class mm{constructor(e,t,n){this._extent=e,this._friction=t||new fm(.01),this._spring=n||new gm(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;var t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){var t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){var e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class _m{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new mm(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){var n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}var r,i,a;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){var o=this._scroll._friction.x(100),s=o%this._itemSize;(r=Math.abs(s)>this._itemSize/2?o-(this._itemSize-Math.abs(s)):o-s)<=0&&r>=-this._extent&&this._scroll.setVelocityByEnd(r)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=(i=this._scroll,function e(t,n,r,i){if(!t||!t.cancelled){r(n);var a=n.done();a||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,r,i))),a&&i&&i(n)}}(a={id:0,cancelled:!1},i,(()=>{var e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();var r=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/r),this._lastTime=e)}),(()=>{this._enableSnap&&(r<=0&&r>=-this._extent&&(this._position=r,this.updatePosition()),fn(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1})),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,a),model:i})}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){var e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),fn(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);var n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(fn(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;var e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){var r=0,i=this._position;this._enableX?(r=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(r=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-r?this._position=-r:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),i!==this._position&&(this.dispatchScroll(),fn(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=r,this._scroll._extent=r}updatePosition(){var e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}const ym=qp({name:"PickerViewColumn",setup(e,t){var n,r,{slots:i,emit:a}=t,o=Qa(null),s=Qa(null),l=Ns("getPickerViewColumn"),u=Ol(),c=l?l(u):Qa(0),d=Ns("pickerViewProps"),h=Ns("pickerViewState"),f=Qa(34),p=Qa(null),v=Fl((()=>(h.height-f.value)/2)),{state:g}=yg(),m=za({current:c.value,length:0});function _(){n&&!r&&(r=!0,_o((()=>{r=!1;var e=Math.min(m.current,m.length-1);e=Math.max(e,0),n.update(e*f.value,void 0,f.value)})))}$o((()=>c.value),(e=>{e!==m.current&&(m.current=e,_())})),$o((()=>m.current),(e=>c.value=e)),$o([()=>f.value,()=>m.length,()=>h.height],_);var y=0;function b(e){var t=y+e.deltaY;if(Math.abs(t)>10){y=0;var r=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=r=Math.max(r,0),n.scrollTo(r*f.value)}else y=t;e.preventDefault()}function w(e){var{clientY:t}=e,r=o.value;if(!n.isScrolling()){var i=t-r.getBoundingClientRect().top-h.height/2,a=f.value/2;if(!(Math.abs(i)<=a)){var s=Math.ceil((Math.abs(i)-a)/f.value),l=i<0?-s:s,u=Math.min(m.current+l,m.length-1);m.current=u=Math.max(u,0),n.scrollTo(u*f.value)}}}var x=()=>{var e,t,r,i=o.value,a=s.value,{scroller:l,handleTouchStart:u,handleTouchMove:c,handleTouchEnd:d}=function(e,t){var n={trackingID:-1,maxDy:0,maxDx:0},r=new _m(e,t);function i(e){var t=e,r=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:r.screenX-n.x,y:r.screenY-n.y}}return{scroller:r,handleTouchStart:function(e){var t=e,i=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=i.screenX,n.y=i.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||i.timeStamp],n.listener=r,r.onTouchStart&&r.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){var t=e,r=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();var a=i(e);if(a){for(n.maxDy=Math.max(n.maxDy,Math.abs(a.y)),n.maxDx=Math.max(n.maxDx,Math.abs(a.x)),n.historyX.push(a.x),n.historyY.push(a.y),n.historyTime.push(t.detail.timeStamp||r.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(a.x,a.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();var t=i(e);if(t){var r=n.listener;n.trackingID=-1,n.listener=null;var a={x:0,y:0};if(n.historyTime.length>2)for(var o=n.historyTime.length-1,s=n.historyTime[o],l=n.historyX[o],u=n.historyY[o];o>0;){o--;var c=s-n.historyTime[o];if(c>30&&c<50){a.x=(l-n.historyX[o])/(c/1e3),a.y=(u-n.historyY[o])/(c/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],r&&r.onTouchEnd&&r.onTouchEnd(t.x,t.y,a)}}}}}(a,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:f.value,friction:new fm(1e-4),spring:new gm(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});n=l,qg(i,(e=>{switch(e.detail.state){case"start":u(e),zg({disable:!0});break;case"move":c(e),e.stopPropagation();break;case"end":case"cancel":d(e),zg({disable:!1})}}),!0),t=0,r=0,(e=i).addEventListener("touchstart",(e=>{var n=e.changedTouches[0];t=n.clientX,r=n.clientY})),e.addEventListener("touchend",(e=>{var n=e.changedTouches[0];if(Math.abs(n.clientX-t)<20&&Math.abs(n.clientY-r)<20){var i={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},a=new CustomEvent("click",i);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{a[e]=n[e]})),e.target.dispatchEvent(a)}})),Dg(),_()},S=!1;return $g((()=>{var e;s.value&&(m.length=s.value.children.length),S||(S=!0,e=p.value,f.value=e.$el.offsetHeight,x())})),()=>{var e=i.default&&i.default(),t="".concat(v.value,"px 0");return vl("uni-picker-view-column",{ref:o},[vl("div",{onWheel:b,onClick:w,class:"uni-picker-view-group"},[vl("div",wl(g.attrs,{class:["uni-picker-view-mask",d.maskClass],style:"background-size: 100% ".concat(v.value,"px;").concat(d.maskStyle)}),null,16),vl("div",wl(g.attrs,{class:["uni-picker-view-indicator",d.indicatorClass],style:d.indicatorStyle}),[vl(sv,{ref:p,onResize:e=>{var{height:t}=e;return f.value=t}},null,8,["onResize"])],16),vl("div",{ref:s,class:["uni-picker-view-content"],style:{padding:t,"--picker-view-column-indicator-height":"".concat(f.value,"px")}},[e],4)],40,["onWheel","onClick"])],512)}}});var bm=er,wm="backwards";const xm=qp({name:"Progress",props:{percent:{type:[Number,String],default:0,validator:e=>!isNaN(parseFloat(e))},fontSize:{type:[String,Number],default:16},showInfo:{type:[Boolean,String],default:!1},strokeWidth:{type:[Number,String],default:6,validator:e=>!isNaN(parseFloat(e))},color:{type:String,default:bm},activeColor:{type:String,default:bm},backgroundColor:{type:String,default:"#EBEBEB"},active:{type:[Boolean,String],default:!1},activeMode:{type:String,default:wm},duration:{type:[Number,String],default:30,validator:e=>!isNaN(parseFloat(e))},borderRadius:{type:[Number,String],default:0}},setup(e){var t=Qa(null),n=function(e){var t=Qa(0),n=Fl((()=>"background-color: ".concat(e.backgroundColor,"; height: ").concat(e.strokeWidth,"px;"))),r=Fl((()=>{var n=e.color!==bm&&e.activeColor===bm?e.color:e.activeColor;return"width: ".concat(t.value,"%;background-color: ").concat(n)})),i=Fl((()=>{if("string"==typeof e.percent&&!/^-?\d*\.?\d*$/.test(e.percent))return 0;var t=parseFloat(e.percent);return Number.isNaN(t)||t<0?t=0:t>100&&(t=100),t})),a=za({outerBarStyle:n,innerBarStyle:r,realPercent:i,currentPercent:t,strokeTimer:0,lastPercent:0});return a}(e);return Sm(n,e),$o((()=>n.realPercent),((t,r)=>{n.strokeTimer&&clearInterval(n.strokeTimer),n.lastPercent=r||0,Sm(n,e)})),()=>{var{showInfo:r}=e,{outerBarStyle:i,innerBarStyle:a,currentPercent:o}=n;return vl("uni-progress",{class:"uni-progress",ref:t},[vl("div",{style:i,class:"uni-progress-bar"},[vl("div",{style:a,class:"uni-progress-inner-bar"},null,4)],4),r?vl("p",{class:"uni-progress-info"},[o+"%"]):""],512)}}});function Sm(e,t){t.active?(e.currentPercent=t.activeMode===wm?0:e.lastPercent,e.strokeTimer=setInterval((()=>{e.currentPercent+1>e.realPercent?(e.currentPercent=e.realPercent,e.strokeTimer&&clearInterval(e.strokeTimer)):e.currentPercent+=1}),parseFloat(t.duration))):e.currentPercent=e.realPercent}var km=Cu("ucg");const Tm=qp({name:"RadioGroup",props:{name:{type:String,default:""}},setup(e,t){var{emit:n,slots:r}=t,i=Qa(null);return function(e,t){var n=[];rs((()=>{s(n.length-1)}));var r=()=>{var e;return null===(e=n.find((e=>e.value.radioChecked)))||void 0===e?void 0:e.value.value};Bs(km,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},radioChange(e,i){s(n.indexOf(i),!0),t("change",e,{value:r()})}});var i=Ns(Jp,!1),a={submit:()=>{var t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=r()),t}};i&&(i.addField(a),os((()=>{i.removeField(a)})));function o(e,t){e.value={radioChecked:t,value:e.value.value}}function s(e,t){n.forEach(((r,i)=>{i!==e&&(t?o(n[i],!1):n.forEach(((e,t)=>{i>=t||n[t].value.radioChecked&&o(n[i],!1)})))}))}}(e,Zp(i,n)),()=>vl("uni-radio-group",{ref:i},[r.default&&r.default()],512)}});const Em=qp({name:"Radio",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:"#ffffff"}},setup(e,t){var{slots:n}=t,r=Qa(null),i=Qa(e.checked),a=Qa(e.value);var o=Fl((()=>function(t){if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};var n={};return i.value?(n.backgroundColor=e.activeBackgroundColor||e.color,n.borderColor=e.activeBorderColor||n.backgroundColor):(e.borderColor&&(n.borderColor=e.borderColor),e.backgroundColor&&(n.backgroundColor=e.backgroundColor)),n}(i.value)));$o([()=>e.checked,()=>e.value],(e=>{var[t,n]=e;i.value=t,a.value=n}));var{uniCheckGroup:s,uniLabel:l,field:u}=function(e,t,n){var r=Fl({get:()=>({radioChecked:Boolean(e.value),value:t.value}),set:t=>{var{radioChecked:n}=t;e.value=n}}),i={reset:n},a=Ns(km,!1);a&&a.addField(r);var o=Ns(Jp,!1);o&&o.addField(i);var s=Ns(tv,!1);return os((()=>{a&&a.removeField(r),o&&o.removeField(i)})),{uniCheckGroup:a,uniForm:o,uniLabel:s,field:r}}(i,a,(()=>{i.value=!1})),c=t=>{e.disabled||i.value||(i.value=!0,s&&s.radioChange(t,u),t.stopPropagation())};return l&&(l.addHandler(c),os((()=>{l.removeHandler(c)}))),rv(e,{"label-click":c}),()=>{var t,a=Kp(e,"disabled");return t=i.value,vl("uni-radio",wl(a,{id:e.id,onClick:c,ref:r}),[vl("div",{class:"uni-radio-wrapper",style:{"--HOVER-BD-COLOR":i.value?o.value.borderColor:e.activeBorderColor}},[vl("div",{class:["uni-radio-input",{"uni-radio-input-disabled":e.disabled}],style:o.value},[t?Au(Iu,e.disabled?"#ADADAD":e.iconColor,18):""],6),n.default&&n.default()],4)],16,["id","onClick"])}}});var Cm={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},Om={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};var Mm=(e,t,n)=>!n||cn(n)&&!n.length?[]:n.map((n=>{var r;if(wn(n)){if(!un(n,"type")||"node"===n.type){var i={[e]:""},a=null==(r=n.name)?void 0:r.toLowerCase();if(!un(Cm,a))return;return function(e,t){if(wn(t))for(var n in t)if(un(t,n)){var r=t[n];"img"===e&&"src"===n&&(t[n]=mc(r))}}(a,n.attrs),i=on(i,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),$l(n.name,i,Mm(e,t,n.children))}return"text"===n.type&&pn(n.text)&&""!==n.text?ml((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(e,t){return un(Om,t)&&Om[t]?Om[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e}))):void 0}}));function Lm(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);var t=[],n={node:"root",children:[]};return jv(e,{start:function(e,r,i){var a={name:e};if(0!==r.length&&(a.attrs=function(e){return e.reduce((function(e,t){var n=t.value,r=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(r)&&(n=n.split(" ")),e[r]?Array.isArray(e[r])?e[r].push(n):e[r]=[e[r],n]:e[r]=n,e}),{})}(r)),i){var o=t[0]||n;o.children||(o.children=[]),o.children.push(a)}else t.unshift(a)},end:function(e){var r=t.shift();if(r.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)n.children.push(r);else{var i=t[0];i.children||(i.children=[]),i.children.push(r)}},chars:function(e){var r={type:"text",text:e};if(0===t.length)n.children.push(r);else{var i=t[0];i.children||(i.children=[]),i.children.push(r)}},comment:function(e){var n={node:"comment",text:e},r=t[0];r&&(r.children||(r.children=[]),r.children.push(n))}}),n.children}const Im=qp({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["click","touchstart","touchmove","touchcancel","touchend","longpress","itemclick"],setup(e,t){var{emit:n}=t,r=Ol(),i=r&&r.vnode.scopeId||"",a=Qa(null),o=Qa([]),s=Zp(a,n);function l(e){s("itemclick",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}return $o((()=>e.nodes),(function(){var t=e.nodes;pn(t)&&(t=Lm(e.nodes)),o.value=Mm(i,l,t)}),{immediate:!0}),()=>$l("uni-rich-text",{ref:a},$l("div",{},o.value))}}),Am=qp({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,t){var{slots:n}=t,r=Qa(null),i=Fl((()=>{var t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t})),a=Fl((()=>{var t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)}));return()=>{var{refreshState:t,refresherDefaultStyle:o,refresherThreshold:s}=e;return vl("div",{ref:r,style:i.value,class:"uni-scroll-view-refresher"},["none"!==o?vl("div",{class:"uni-scroll-view-refresh"},[vl("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==t?vl("svg",{key:"refresh__icon",style:{transform:"rotate("+a.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[vl("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),vl("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==t?vl("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[vl("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===o?vl("div",{class:"uni-scroll-view-refresher-container",style:{height:"".concat(s,"px")}},[n.default&&n.default()]):null],4)}}});var Bm=yr(!0);const Nm=qp({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,t){var{emit:n,slots:r,expose:i}=t,a=Qa(null),o=Qa(null),s=Qa(null),l=Qa(null),u=Zp(a,n),{state:c,scrollTopNumber:d,scrollLeftNumber:h}=function(e){var t=Fl((()=>Number(e.scrollTop)||0)),n=Fl((()=>Number(e.scrollLeft)||0)),r=za({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""});return{state:r,scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:f,realScrollY:p,_scrollLeftChanged:v,_scrollTopChanged:g}=function(e,t,n,r,i,a,o,s,l){var u=!1,c=0,d=!1,h=()=>{},f=Fl((()=>e.scrollX)),p=Fl((()=>e.scrollY)),v=Fl((()=>{var t=Number(e.upperThreshold);return isNaN(t)?50:t})),g=Fl((()=>{var t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function m(e,t){var n=o.value,r=0,i="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?r=n.scrollLeft-e:"y"===t&&(r=n.scrollTop-e),0!==r){var a=s.value;a.style.transition="transform .3s ease-out",a.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?i="translateX("+r+"px) translateZ(0)":"y"===t&&(i="translateY("+r+"px) translateZ(0)"),a.removeEventListener("transitionend",h),a.removeEventListener("webkitTransitionEnd",h),h=()=>x(e,t),a.addEventListener("transitionend",h),a.addEventListener("webkitTransitionEnd",h),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),a.style.transform=i,a.style.webkitTransform=i}}function _(e){var n=e.target;i("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),p.value&&(n.scrollTop<=v.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+g.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),f.value&&(n.scrollLeft<=v.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+g.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function y(t){p.value&&(e.scrollWithAnimation?m(t,"y"):o.value.scrollTop=t)}function b(t){f.value&&(e.scrollWithAnimation?m(t,"x"):o.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error("id error: scroll-into-view=".concat(t));var n=a.value.querySelector("#"+t);if(n){var r=o.value.getBoundingClientRect(),i=n.getBoundingClientRect();if(f.value){var s=i.left-r.left,l=o.value.scrollLeft+s;e.scrollWithAnimation?m(l,"x"):o.value.scrollLeft=l}if(p.value){var u=i.top-r.top,c=o.value.scrollTop+u;e.scrollWithAnimation?m(c,"y"):o.value.scrollTop=c}}}}function x(e,t){s.value.style.transition="",s.value.style.webkitTransition="",s.value.style.transform="",s.value.style.webkitTransform="";var n=o.value;"x"===t?(n.style.overflowX=f.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=p.value?"auto":"hidden",n.scrollTop=e),s.value.removeEventListener("transitionend",h),s.value.removeEventListener("webkitTransitionEnd",h)}function S(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,u||(u=!0,i("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),i("refresherrefresh",{},{dy:T.y-k.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":u=!1,t.refresherHeight=c=0,"restore"===n&&(d=!1,i("refresherrestore",{},{dy:T.y-k.y})),"refresherabort"===n&&d&&(d=!1,i("refresherabort",{},{dy:T.y-k.y}))}t.refreshState=n}}var k={x:0,y:0},T={x:0,y:e.refresherThreshold};return rs((()=>{_o((()=>{y(n.value),b(r.value)})),w(e.scrollIntoView);var a=function(e){e.preventDefault(),e.stopPropagation(),_(e)},s=null,l=function(n){if(null!==k){var r=n.touches[0].pageX,a=n.touches[0].pageY,l=o.value;if(Math.abs(r-k.x)>Math.abs(a-k.y))if(f.value){if(0===l.scrollLeft&&r>k.x)return void(s=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&r<k.x)return void(s=!1);s=!0}else s=!1;else if(p.value)if(0===l.scrollTop&&a>k.y)s=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&a<k.y)return void(s=!1);s=!0}else s=!1;if(s&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&S("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){var h=a-k.y;0===c&&(c=a),u?(t.refresherHeight=h+e.refresherThreshold,d=!1):(t.refresherHeight=a-c,t.refresherHeight>0&&(d=!0,i("refresherpulling",n,{deltaY:h,dy:h})))}}},h=function(e){1===e.touches.length&&(zg({disable:!0}),k={x:e.touches[0].pageX,y:e.touches[0].pageY})},v=function(n){T={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},zg({disable:!1}),t.refresherHeight>=e.refresherThreshold?S("refreshing"):S("refresherabort"),k={x:0,y:0},T={x:0,y:e.refresherThreshold}};o.value.addEventListener("touchstart",h,Bm),o.value.addEventListener("touchmove",l,yr(!1)),o.value.addEventListener("scroll",a,yr(!1)),o.value.addEventListener("touchend",v,Bm),Dg(),os((()=>{o.value.removeEventListener("touchstart",h),o.value.removeEventListener("touchmove",l),o.value.removeEventListener("scroll",a),o.value.removeEventListener("touchend",v)}))})),Go((()=>{p.value&&(o.value.scrollTop=t.lastScrollTop),f.value&&(o.value.scrollLeft=t.lastScrollLeft)})),$o(n,(e=>{y(e)})),$o(r,(e=>{b(e)})),$o((()=>e.scrollIntoView),(e=>{w(e)})),$o((()=>e.refresherTriggered),(e=>{!0===e?S("refreshing"):!1===e&&S("restore")})),{realScrollX:f,realScrollY:p,_scrollTopChanged:y,_scrollLeftChanged:b}}(e,c,d,h,u,a,o,l,n),m=Fl((()=>{var e="";return f.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",p.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),_=Fl((()=>{var t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return i({$getMain:()=>o.value}),()=>{var{refresherEnabled:t,refresherBackground:n,refresherDefaultStyle:i,refresherThreshold:u}=e,{refresherHeight:d,refreshState:h}=c;return vl("uni-scroll-view",{ref:a},[vl("div",{ref:s,class:"uni-scroll-view"},[vl("div",{ref:o,style:m.value,class:_.value},[t?vl(Am,{refreshState:h,refresherHeight:d,refresherThreshold:u,refresherDefaultStyle:i,refresherBackground:n},{default:()=>["none"==i?r.refresher&&r.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,vl("div",{ref:l,class:"uni-scroll-view-content"},[r.default&&r.default()],512)],6)],512)],512)}}});const Rm=qp({name:"Slider",props:{name:{type:String,default:""},min:{type:[Number,String],default:0},max:{type:[Number,String],default:100},value:{type:[Number,String],default:0},step:{type:[Number,String],default:1},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#e9e9e9"},backgroundColor:{type:String,default:"#e9e9e9"},activeColor:{type:String,default:"#007aff"},selectedColor:{type:String,default:"#007aff"},blockColor:{type:String,default:"#ffffff"},blockSize:{type:[Number,String],default:28},showValue:{type:[Boolean,String],default:!1}},emits:["changing","change"],setup(e,t){var{emit:n}=t,r=Qa(null),i=Qa(null),a=Qa(null),o=Qa(Number(e.value));$o((()=>e.value),(e=>{o.value=Number(e)}));var s=Zp(r,n),l=function(e,t){var n=()=>Pm(t.value,e.min,e.max),r=()=>"#e9e9e9"!==e.backgroundColor?e.backgroundColor:"#007aff"!==e.color?e.color:"#007aff",i=()=>"#007aff"!==e.activeColor?e.activeColor:"#e9e9e9"!==e.selectedColor?e.selectedColor:"#e9e9e9",a={setBgColor:Fl((()=>({backgroundColor:r()}))),setBlockBg:Fl((()=>({left:n()}))),setActiveColor:Fl((()=>({backgroundColor:i(),width:n()}))),setBlockStyle:Fl((()=>({width:e.blockSize+"px",height:e.blockSize+"px",marginLeft:-e.blockSize/2+"px",marginTop:-e.blockSize/2+"px",left:n(),backgroundColor:e.blockColor})))};return a}(e,o),{_onClick:u,_onTrack:c}=function(e,t,n,r,i){var a=n=>{e.disabled||(s(n),i("change",n,{value:t.value}))},o=t=>{var n=Number(e.max),r=Number(e.min),i=Number(e.step);return t<r?r:t>n?n:Dm.mul.call(Math.round((t-r)/i),i)+r},s=i=>{var a=Number(e.max),s=Number(e.min),l=r.value,u=getComputedStyle(l,null).marginLeft,c=l.offsetWidth;c+=parseInt(u);var d=n.value,h=d.offsetWidth-(e.showValue?c:0),f=d.getBoundingClientRect().left,p=(i.x-f)*(a-s)/h+s;t.value=o(p)},l=n=>{if(!e.disabled)return"move"===n.detail.state?(s({x:n.detail.x}),i("changing",n,{value:t.value}),!1):"end"===n.detail.state&&i("change",n,{value:t.value})},u=Ns(Jp,!1);if(u){var c={reset:()=>t.value=Number(e.min),submit:()=>{var n=["",null];return""!==e.name&&(n[0]=e.name,n[1]=t.value),n}};u.addField(c),os((()=>{u.removeField(c)}))}return{_onClick:a,_onTrack:l}}(e,o,r,i,s);return rs((()=>{qg(a.value,c)})),()=>{var{setBgColor:t,setBlockBg:n,setActiveColor:s,setBlockStyle:c}=l;return vl("uni-slider",{ref:r,onClick:Xp(u)},[vl("div",{class:"uni-slider-wrapper"},[vl("div",{class:"uni-slider-tap-area"},[vl("div",{style:t.value,class:"uni-slider-handle-wrapper"},[vl("div",{ref:a,style:n.value,class:"uni-slider-handle"},null,4),vl("div",{style:c.value,class:"uni-slider-thumb"},null,4),vl("div",{style:s.value,class:"uni-slider-track"},null,4)],4)]),Uo(vl("span",{ref:i,class:"uni-slider-value"},[o.value],512),[[Xl,e.showValue]])]),vl("slot",null,null)],8,["onClick"])}}});var Pm=(e,t,n)=>(n=Number(n),100*(e-(t=Number(t)))/(n-t)+"%");var Dm={mul:function(e){var t=0,n=this.toString(),r=e.toString();try{t+=n.split(".")[1].length}catch(i){}try{t+=r.split(".")[1].length}catch(i){}return Number(n.replace(".",""))*Number(r.replace(".",""))/Math.pow(10,t)}};function zm(e,t,n,r,i,a){function o(){u&&(clearTimeout(u),u=null)}var s,l,u=null,c=!0,d=0,h=1,f=null,p=!1,v=0,g="",m=Fl((()=>n.value.length>t.displayMultipleItems)),_=Fl((()=>e.circular&&m.value));function y(i){Math.floor(2*d)===Math.floor(2*i)&&Math.ceil(2*d)===Math.ceil(2*i)||_.value&&function(r){if(!c)for(var i=n.value,a=i.length,o=r+t.displayMultipleItems,s=0;s<a;s++){var l=i[s],u=Math.floor(r/a)*a+s,d=u+a,h=u-a,f=Math.max(r-(u+1),u-o,0),p=Math.max(r-(d+1),d-o,0),v=Math.max(r-(h+1),h-o,0),g=Math.min(f,p,v),m=[u,d,h][[f,p,v].indexOf(g)];l.updatePosition(m,e.vertical)}}(i);var o="translate("+(e.vertical?"0":100*-i*h+"%")+", "+(e.vertical?100*-i*h+"%":"0")+") translateZ(0)",l=r.value;if(l&&(l.style.webkitTransform=o,l.style.transform=o),d=i,!s){if(i%1==0)return;s=i}i-=Math.floor(s);var u=n.value;i<=-(u.length-1)?i+=u.length:i>=u.length&&(i-=u.length),i=s%1>.5||s<0?i-1:i,a("transition",{},{dx:e.vertical?0:i*l.offsetWidth,dy:e.vertical?i*l.offsetHeight:0})}function b(e){var r=n.value.length;if(!r)return-1;var i=(Math.round(e)%r+r)%r;if(_.value){if(r<=t.displayMultipleItems)return 0}else if(i>r-t.displayMultipleItems)return r-t.displayMultipleItems;return i}function w(){f=null}function x(){if(f){var e=f,r=e.toPos,i=e.acc,o=e.endTime,u=e.source,c=o-Date.now();if(c<=0){y(r),f=null,p=!1,s=null;var d=n.value[t.current];if(d){var h=d.getItemId();a("animationfinish",{},{current:t.current,currentItemId:h,source:u})}}else{y(r+i*c*c/2),l=requestAnimationFrame(x)}}else p=!1}function S(e,r,i){w();var a=t.duration,o=n.value.length,s=d;if(_.value)if(i<0){for(;s<e;)s+=o;for(;s-o>e;)s-=o}else if(i>0){for(;s>e;)s-=o;for(;s+o<e;)s+=o;s+o-e<e-s&&(s+=o)}else{for(;s+o<e;)s+=o;for(;s-o>e;)s-=o;s+o-e<e-s&&(s+=o)}else"click"===r&&(e=e+t.displayMultipleItems-1<o?e:0);f={toPos:e,acc:2*(s-e)/(a*a),endTime:Date.now()+a,source:r},p||(p=!0,l=requestAnimationFrame(x))}function k(){o();var e=n.value,r=function(){u=null,g="autoplay",_.value?t.current=b(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",_.value?1:0),u=setTimeout(r,t.interval)};c||e.length<=t.displayMultipleItems||(u=setTimeout(r,t.interval))}function T(e){e?k():o()}return $o([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{var r=-1;if(e.currentItemId)for(var i=0,a=n.value;i<a.length;i++){if(a[i].getItemId()===e.currentItemId){r=i;break}}r<0&&(r=Math.round(e.current)||0),r=r<0?0:r,t.current!==r&&(g="",t.current=r)})),$o([()=>e.vertical,()=>_.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){o(),f&&(y(f.toPos),f=null);for(var i=n.value,a=0;a<i.length;a++)i[a].updatePosition(a,e.vertical);h=1;var s=r.value;if(1===t.displayMultipleItems&&i.length){var l=i[0].getBoundingClientRect(),u=s.getBoundingClientRect();(h=l.width/u.width)>0&&h<1||(h=1)}var p=d;d=-2;var g=t.current;g>=0?(c=!1,t.userTracking?(y(p+g-v),v=g):(y(g),e.autoplay&&k())):(c=!0,y(-t.displayMultipleItems-1))})),$o((()=>t.interval),(()=>{u&&(o(),k())})),$o((()=>t.current),((e,r)=>{!function(e,r){var i=g;g="";var o=n.value;if(!i){var s=o.length;S(e,"",_.value&&r+(s-e)%s>s/2?1:0)}var l=o[e];if(l){var u=t.currentItemId=l.getItemId();a("change",{},{current:t.current,currentItemId:u,source:i})}}(e,r),i("update:current",e)})),$o((()=>t.currentItemId),(e=>{i("update:currentItemId",e)})),$o((()=>e.autoplay&&!t.userTracking),T),T(e.autoplay&&!t.userTracking),rs((()=>{var i=!1,a=0,s=0;function l(e){t.userTracking=!1;var n=a/Math.abs(a),r=0;!e&&Math.abs(a)>.2&&(r=.5*n);var i=b(d+r);e?y(v):(g="touch",t.current=i,S(i,"touch",0!==r?r:0===i&&_.value&&d>=1?1:0))}qg(r.value,(u=>{if(!e.disableTouch&&!c){if("start"===u.detail.state)return t.userTracking=!0,i=!1,o(),v=d,a=0,s=Date.now(),void w();if("end"===u.detail.state)return l(!1);if("cancel"===u.detail.state)return l(!0);if(t.userTracking){if(!i){i=!0;var h=Math.abs(u.detail.dx),f=Math.abs(u.detail.dy);if((h>=f&&e.vertical||h<=f&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&k())}return function(i){var o=s;s=Date.now();var l=n.value.length-t.displayMultipleItems;function u(e){return.5-.25/(e+.5)}function c(e,t){var n=v+e;a=.6*a+.4*t,_.value||(n<0||n>l)&&(n<0?n=-u(-n):n>l&&(n=l+u(n-l)),a=0),y(n)}var d=s-o||1,h=r.value;e.vertical?c(-i.dy/h.offsetHeight,-i.ddy/d):c(-i.dx/h.offsetWidth,-i.ddx/d)}(u.detail),!1}}}))})),ss((()=>{o(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){S(t.current=e,g="click",_.value?1:0)},circularEnabled:_,swiperEnabled:m}}const Fm=qp({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,t){var{slots:n,emit:r}=t,i=Qa(null),a=Zp(i,r),o=Qa(null),s=Qa(null),l=function(e){return za({interval:Fl((()=>{var t=Number(e.interval);return isNaN(t)?5e3:t})),duration:Fl((()=>{var t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:Fl((()=>{var t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),u=Fl((()=>{var t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:Mu(e.previousMargin,!0),bottom:Mu(e.nextMargin,!0)}:{top:0,bottom:0,left:Mu(e.previousMargin,!0),right:Mu(e.nextMargin,!0)}),t})),c=Fl((()=>{var t=Math.abs(100/l.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}})),d=[],h=[],f=Qa([]);function p(){for(var e=[],t=function(t){var n=d[t];n instanceof Element||(n=n.el);var r=h.find((e=>n===e.rootRef.value));r&&e.push(qa(r))},n=0;n<d.length;n++)t(n);f.value=e}$g((()=>{d=s.value.children,p()}));Bs("addSwiperContext",(function(e){h.push(e),p()}));Bs("removeSwiperContext",(function(e){var t=h.indexOf(e);t>=0&&(h.splice(t,1),p())}));var{onSwiperDotClick:v,circularEnabled:g,swiperEnabled:m}=zm(e,l,f,s,r,a);return()=>{var t=n.default&&n.default();return d=Fg(t),vl("uni-swiper",{ref:i},[vl("div",{ref:o,class:"uni-swiper-wrapper"},[vl("div",{class:"uni-swiper-slides",style:u.value},[vl("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[t],4)],4),e.indicatorDots&&vl("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map(((t,n,r)=>vl("div",{onClick:()=>v(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<l.current+l.displayMultipleItems&&n>=l.current||n<l.current+l.displayMultipleItems-r.length},style:{background:n===l.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),null],512)],512)}}});const $m=qp({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,t){var{slots:n}=t,r=Qa(null),i={rootRef:r,getItemId:()=>e.itemId,getBoundingClientRect:()=>r.value.getBoundingClientRect(),updatePosition(e,t){var n=t?"0":100*e+"%",i=t?100*e+"%":"0",a=r.value,o="translate(".concat(n,",").concat(i,") translateZ(0)");a&&(a.style.webkitTransform=o,a.style.transform=o)}};return rs((()=>{var e=Ns("addSwiperContext");e&&e(i)})),ss((()=>{var e=Ns("removeSwiperContext");e&&e(i)})),()=>vl("uni-swiper-item",{ref:r,style:{position:"absolute",width:"100%",height:"100%"}},[n.default&&n.default()],512)}});const jm=qp({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,t){var{emit:n}=t,r=Qa(null),i=Qa(e.checked),a=function(e,t){var n=Ns(Jp,!1),r=Ns(tv,!1),i={submit:()=>{var n=["",null];return e.name&&(n[0]=e.name,n[1]=t.value),n},reset:()=>{t.value=!1}};n&&(n.addField(i),ss((()=>{n.removeField(i)})));return r}(e,i),o=Zp(r,n);$o((()=>e.checked),(e=>{i.value=e}));var s=t=>{e.disabled||(i.value=!i.value,o("change",t,{value:i.value}))};return a&&(a.addHandler(s),os((()=>{a.removeHandler(s)}))),rv(e,{"label-click":s}),()=>{var t,{color:n,type:a}=e,o=Kp(e,"disabled"),l={};return n&&i.value&&(l.backgroundColor=n,l.borderColor=n),t=i.value,vl("uni-switch",wl({id:e.id,ref:r},o,{onClick:s}),[vl("div",{class:"uni-switch-wrapper"},[Uo(vl("div",{class:["uni-switch-input",[i.value?"uni-switch-input-checked":""]],style:l},null,6),[[Xl,"switch"===a]]),Uo(vl("div",{class:"uni-checkbox-input"},[t?Au(Iu,e.color,22):""],512),[[Xl,"checkbox"===a]])])],16,["id","onClick"])}}});var Vm={ensp:" ",emsp:" ",nbsp:" "};function Wm(e,t){return function(e,t){var{space:n,decode:r}=t,i="",a=!1;for(var o of e)n&&Vm[n]&&" "===o&&(o=Vm[n]),a?(i+="n"===o?Jn:"\\"===o?"\\":"\\"+o,a=!1):"\\"===o?a=!0:i+=o;return r?i.replace(/&nbsp;/g,Vm.nbsp).replace(/&ensp;/g,Vm.ensp).replace(/&emsp;/g,Vm.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):i}(e,t).split(Jn)}function Hm(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Um(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Hm(Object(n),!0).forEach((function(t){Ym(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Hm(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function qm(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}function Ym(e,t,n){return(t=qm(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Xm=on({},Tg,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>Gm.concat("return").includes(e)}}),Zm=!1,Gm=["done","go","next","search","send"];const Km=qp({name:"Textarea",props:Xm,emits:["confirm","linechange",...Eg],setup(e,t){var n,{emit:r,expose:i}=t,a=Qa(null),o=Qa(null),{fieldRef:s,state:l,scopedAttrsState:u,fixDisabledColor:c,trigger:d}=Mg(e,a,r),h=Fl((()=>l.value.split(Jn))),f=Fl((()=>Gm.includes(e.confirmType))),p=Qa(0),v=Qa(null);function g(e){var{height:t}=e;p.value=t}function m(e){"Enter"===e.key&&f.value&&e.preventDefault()}function _(t){if("Enter"===t.key&&f.value){!function(e){d("confirm",e,{value:l.value})}(t);var n=t.target;!e.confirmHold&&n.blur()}}return $o((()=>p.value),(t=>{var n=a.value,r=v.value,i=o.value,s=parseFloat(getComputedStyle(n).lineHeight);isNaN(s)&&(s=r.offsetHeight);var l=Math.round(t/s);d("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(n.style.height="auto",i.style.height=t+"px")})),n="(prefers-color-scheme: dark)",Zm=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(n).media!==n,i({$triggerInput:e=>{r("update:modelValue",e.value),r("update:value",e.value),l.value=e.value}}),()=>{var t=e.disabled&&c?vl("textarea",{key:"disabled-textarea",ref:s,value:l.value,tabindex:"-1",readonly:!!e.disabled,maxlength:l.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Zm},style:Um({overflowY:e.autoHeight?"hidden":"auto"},e.cursorColor&&{caretColor:e.cursorColor}),onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):vl("textarea",{key:"textarea",ref:s,value:l.value,disabled:!!e.disabled,maxlength:l.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Zm},style:Um({overflowY:e.autoHeight?"hidden":"auto"},e.cursorColor&&{caretColor:e.cursorColor}),onKeydown:m,onKeyup:_},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return vl("uni-textarea",{ref:a},[vl("div",{ref:o,class:"uni-textarea-wrapper"},[Uo(vl("div",wl(u.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Xl,!l.value.length]]),vl("div",{ref:v,class:"uni-textarea-line"},[" "],512),vl("div",{class:"uni-textarea-compute"},[h.value.map((e=>vl("div",null,[e.trim()?e:"."]))),vl(sv,{initial:!0,onResize:g},null,8,["initial","onResize"])]),"search"===e.confirmType?vl("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}});function Jm(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function Qm(e,t,n){e&&_i(n||Nu(),e,((e,n)=>{var{type:r,data:i}=e;t(r,i,n)}))}function e_(e,t){e&&function(e,t){t=mi(e,t),delete gi[t]}(t||Nu(),e)}function t_(e,t,n,r){var i=Ol().proxy;rs((()=>{Qm(t||Jm(i),e,r),!n&&t||$o((()=>i.id),((t,n)=>{Qm(Jm(i,t),e,r),e_(n&&Jm(i,n))}))})),os((()=>{e_(t||Jm(i),r)}))}var n_=0;function r_(e){var t=Bu(),n=Ol().proxy,r=n.$options.name.toLowerCase(),i=e||n.id||"context".concat(n_++);return rs((()=>{n.$el.__uniContextInfo={id:i,type:r,page:t}})),"".concat(r,".").concat(i)}class i_ extends Vp{constructor(e,t,n,r,i){super(e,t,n,r,i,[...Up.props,...arguments.length>5&&void 0!==arguments[5]?arguments[5]:[]])}call(e){var t={animation:this.$props.animation,$el:this.$};e.call(t)}setAttribute(e,t){return"animation"===e&&(this.$animate=!0),super.setAttribute(e,t)}update(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.$animate)return e?this.call(Up.mounted):void(this.$animate&&(this.$animate=!1,this.call(Up.watch.animation.handler)))}}var a_=["space","decode"];var o_=["hover-class","hover-stop-propagation","hover-start-time","hover-stay-time"];class s_ extends i_{constructor(e,t,n,r,i){super(e,t,n,r,i,[...o_,...arguments.length>5&&void 0!==arguments[5]?arguments[5]:[]])}update(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.$props["hover-class"];t&&"none"!==t?(this._hover||(this._hover=new l_(this.$,this.$props)),this._hover.addEvent()):this._hover&&this._hover.removeEvent(),super.update(e)}}class l_{constructor(e,t){this._listening=!1,this._hovering=!1,this._hoverTouch=!1,this.$=e,this.props=t,this.__hoverTouchStart=this._hoverTouchStart.bind(this),this.__hoverTouchEnd=this._hoverTouchEnd.bind(this),this.__hoverTouchCancel=this._hoverTouchCancel.bind(this)}get hovering(){return this._hovering}set hovering(e){this._hovering=e;var t=this.props["hover-class"].split(" ").filter(Boolean),n=this.$.classList;e?this.$.classList.add.apply(n,t):this.$.classList.remove.apply(n,t)}addEvent(){this._listening||(this._listening=!0,this.$.addEventListener("touchstart",this.__hoverTouchStart),this.$.addEventListener("touchend",this.__hoverTouchEnd),this.$.addEventListener("touchcancel",this.__hoverTouchCancel))}removeEvent(){this._listening&&(this._listening=!1,this.$.removeEventListener("touchstart",this.__hoverTouchStart),this.$.removeEventListener("touchend",this.__hoverTouchEnd),this.$.removeEventListener("touchcancel",this.__hoverTouchCancel))}_hoverTouchStart(e){if(!e._hoverPropagationStopped){var t=this.props["hover-class"];t&&"none"!==t&&!this.$.disabled&&(e.touches.length>1||(this.props["hover-stop-propagation"]&&(e._hoverPropagationStopped=!0),this._hoverTouch=!0,this._hoverStartTimer=setTimeout((()=>{this.hovering=!0,this._hoverTouch||this._hoverReset()}),this.props["hover-start-time"])))}}_hoverTouchEnd(){this._hoverTouch=!1,this.hovering&&this._hoverReset()}_hoverReset(){requestAnimationFrame((()=>{clearTimeout(this._hoverStayTimer),this._hoverStayTimer=setTimeout((()=>{this.hovering=!1}),this.props["hover-stay-time"])}))}_hoverTouchCancel(){this._hoverTouch=!1,this.hovering=!1,clearTimeout(this._hoverStartTimer)}}function u_(){return plus.navigator.isImmersedStatusbar()?Math.round("iOS"===plus.os.name?plus.navigator.getSafeAreaInsets().top:plus.navigator.getStatusbarHeight()):0}function c_(){var e=plus.webview.currentWebview().getStyle(),t=e&&e.titleNView;return t&&"default"===t.type?Qn+u_():0}var d_=Symbol("onDraw");function h_(e,t){return Fl((()=>{var n={};return Object.keys(e).forEach((r=>{if(!t||!t.includes(r)){var i=e[r];i="src"===r?mc(i):i,n[r.replace(/[A-Z]/g,(e=>"-"+e.toLowerCase()))]=i}})),n}))}function f_(e){var t=za({top:"0px",left:"0px",width:"0px",height:"0px",position:"static"}),n=Qa(!1);function r(){var r=e.value,i=r.getBoundingClientRect(),a=["width","height"];n.value=0===i.width||0===i.height,n.value||(t.position=function(e){for(var t;e;){var n=getComputedStyle(e),r=n.transform||n.webkitTransform;t=(!r||"none"===r)&&t,t="fixed"===n.position||t,e=e.parentElement}return t}(r)?"absolute":"static",a.push("top","left")),a.forEach((e=>{var n=i[e];n="top"===e?n+("static"===t.position?document.documentElement.scrollTop||document.body.scrollTop||0:c_()):n,t[e]=n+"px"}))}var i=null;function a(){i&&cancelAnimationFrame(i),i=requestAnimationFrame((()=>{i=null,r()}))}window.addEventListener("updateview",a);var o=[],s=[];return Bs(d_,(function(e){o?o.push(e):e(t)})),rs((()=>{r(),s.forEach((e=>e())),s=null})),os((()=>{window.removeEventListener("updateview",a)})),{position:t,hidden:n,onParentReady:function(e){var n=Ns(d_),r=n=>{e(n),o.forEach((e=>e(t))),o=null};!function(e){s?s.push(e):e()}((()=>{n?n(r):r({top:"0px",left:"0px",width:Number.MAX_SAFE_INTEGER+"px",height:Number.MAX_SAFE_INTEGER+"px",position:"static"})}))}}}const p_=qp({name:"Ad",props:{adpid:{type:[Number,String],default:""},data:{type:Object,default:null},dataCount:{type:Number,default:5},channel:{type:String,default:""}},setup(e,t){var n,{emit:r}=t,i=Qa(null),a=Qa(null),o=Zp(i,r),s=h_(e,["id"]),{position:l,onParentReady:u}=f_(a);return u((()=>{function t(){var t={adpid:e.adpid,width:l.width,count:e.dataCount};void 0!==e.channel&&(t.ext={channel:e.channel}),UniViewJSBridge.invokeServiceMethod("getAdData",t,(e=>{var{code:t,data:r,message:i}=e;0===t?n.renderingBind(r):o("error",{},{errMsg:i})}))}n=plus.ad.createAdView(Object.assign({},s.value,l)),plus.webview.currentWebview().append(n),n.setDislikeListener((e=>{a.value.style.height="0",window.dispatchEvent(new CustomEvent("updateview")),o("close",{},e)})),n.setRenderingListener((e=>{0===e.result?(a.value.style.height=e.height+"px",window.dispatchEvent(new CustomEvent("updateview"))):o("error",{},{errCode:e.result})})),n.setAdClickedListener((()=>{o("adclicked",{},{})})),$o((()=>l),(e=>n.setStyle(e)),{deep:!0}),$o((()=>e.adpid),(e=>{e&&t()})),$o((()=>e.data),(e=>{e&&n.renderingBind(e)})),e.adpid&&t()})),os((()=>{n&&n.close()})),()=>vl("uni-ad",{ref:i},[vl("div",{ref:a,class:"uni-ad-container"},null,512)],512)}});class v_ extends gp{constructor(e,t,n,r,i,a,o){super(e,t,r);var s=document.createElement("div");s.__vueParent=function(e){for(;e&&e.pid>0;)if(e=tp(e.pid)){var{__vueParentComponent:t}=e.$;if(t)return t}return null}(this),this.$props=za({}),this.init(a),this.$app=fu(function(e,t){return()=>$l(e,t)}(n,this.$props)),this.$app.mount(s),this.$=s.firstElementChild,this.$.__id=e,o&&(this.$holder=this.$.querySelector(o)),un(a,"t")&&this.setText(a.t||""),a.a&&un(a.a,Cr)&&jp(this.$,a.a[Cr]),this.insert(r,i),xo()}init(e){var{a:t,e:n,w:r}=e;t&&(this.setWxsProps(t),Object.keys(t).forEach((e=>{this.setAttr(e,t[e])}))),un(e,"s")&&this.setAttr("style",e.s),n&&Object.keys(n).forEach((e=>{this.addEvent(e,n[e])})),r&&this.addWxsEvents(e.w)}setText(e){(this.$holder||this.$).textContent=e,this.updateView()}addWxsEvent(e,t,n){this.$props[e]=$p(this,t,n)}addEvent(e,t){this.$props[e]=zp(this.id,t,Sr(e)[1])}removeEvent(e){this.$props[e]=null}setAttr(e,t){if(e===Cr)this.$&&jp(this.$,t);else if(e===Or)this.$.__ownerId=t;else if(e===Mr)ip((()=>fp(this,t)),3);else if(e===Er){var n=pp(t,this.$||tp(this.pid).$),r=this.$props.style;wn(n)&&wn(r)?Object.keys(n).forEach((e=>{r[e]=n[e]})):this.$props.style=n}else vp(e)?this.$.style.setProperty(e,_p(t)):(t=pp(t,this.$||tp(this.pid).$),this.wxsPropsInvoke(e,t,!0)||(this.$props[e]=t));this.updateView()}removeAttr(e){vp(e)?this.$.style.removeProperty(e):this.$props[e]=null,this.updateView()}remove(){this.removeUniParent(),this.isUnmounted=!0,this.$app.unmount(),np(this.id),this.removeUniChildren(),this.updateView()}appendChild(e){var t=(this.$holder||this.$).appendChild(e);return this.updateView(!0),t}insertBefore(e,t){var n=(this.$holder||this.$).insertBefore(e,t);return this.updateView(!0),n}}class g_ extends v_{constructor(e,t,n,r,i,a,o){super(e,t,n,r,i,a,o)}getRebuildFn(){return this._rebuild||(this._rebuild=this.rebuild.bind(this)),this._rebuild}setText(e){return ip(this.getRebuildFn(),2),super.setText(e)}appendChild(e){return ip(this.getRebuildFn(),2),super.appendChild(e)}insertBefore(e,t){return ip(this.getRebuildFn(),2),super.insertBefore(e,t)}removeUniChild(e){return ip(this.getRebuildFn(),2),super.removeUniChild(e)}rebuild(){var e=this.$.__vueParentComponent;e.rebuild&&e.rebuild()}}function m_(e,t,n){e.childNodes.forEach((n=>{n instanceof Element?-1===n.className.indexOf(t)&&e.removeChild(n):e.removeChild(n)})),e.appendChild(document.createTextNode(n))}var __=["value","modelValue"];function y_(e){__.forEach((t=>{if(un(e,t)){var n="onUpdate:"+t;un(e,n)||(e[n]=n=>e[t]=n)}}))}class b_ extends gp{constructor(e,t,n,r){super(e,t,n),this.insert(n,r)}}var w_=0;function x_(e,t,n){var r,i,{position:a,hidden:o,onParentReady:s}=f_(e);s((s=>{var l=Fl((()=>{var e={};for(var t in a){var n=a[t],r=parseFloat(n),i=parseFloat(s[t]);if("top"===t||"left"===t)n=Math.max(r,i)+"px";else if("width"===t||"height"===t){var o="width"===t?"left":"top",l=parseFloat(s[o]),u=parseFloat(a[o]),c=Math.max(l-u,0),d=Math.max(u+r-(l+i),0);n=Math.max(r-c-d,0)+"px"}e[t]=n}return e})),u=["borderRadius","borderColor","borderWidth","backgroundColor"],c=["paddingTop","paddingRight","paddingBottom","paddingLeft","color","textAlign","lineHeight","fontSize","fontWeight","textOverflow","whiteSpace"],d=[],h={start:"left",end:"right"};function f(t){var n=getComputedStyle(e.value);return u.concat(c,d).forEach((e=>{t[e]=n[e]})),t}var p=za(f({})),v=null;i=function(){v&&cancelAnimationFrame(v),v=requestAnimationFrame((()=>{v=null,f(p)}))},window.addEventListener("updateview",i);var g=Fl((()=>{var e=function(){var e={};for(var t in e){var n=e[t];"top"!==t&&"left"!==t||(n=Math.min(parseFloat(n)-parseFloat(s[t]),0)+"px"),e[t]=n}return e}(),t=[{tag:"rect",position:e,rectStyles:{color:p.backgroundColor,radius:p.borderRadius,borderColor:p.borderColor,borderWidth:p.borderWidth}}];if("src"in n)n.src&&t.push({tag:"img",position:e,src:n.src});else{var r=parseFloat(p.lineHeight)-parseFloat(p.fontSize),i=parseFloat(e.width)-parseFloat(p.paddingLeft)-parseFloat(p.paddingRight);i=i<0?0:i;var a=parseFloat(e.height)-parseFloat(p.paddingTop)-r/2-parseFloat(p.paddingBottom);a=a<0?0:a,t.push({tag:"font",position:{top:"".concat(parseFloat(e.top)+parseFloat(p.paddingTop)+r/2,"px"),left:"".concat(parseFloat(e.left)+parseFloat(p.paddingLeft),"px"),width:"".concat(i,"px"),height:"".concat(a,"px")},textStyles:{align:h[p.textAlign]||p.textAlign,color:p.color,decoration:"none",lineSpacing:"".concat(r,"px"),margin:"0px",overflow:p.textOverflow,size:p.fontSize,verticalAlign:"top",weight:p.fontWeight,whiteSpace:p.whiteSpace},text:n.text})}return t}));r=new plus.nativeObj.View("cover-".concat(Date.now(),"-").concat(w_++),l.value,g.value),plus.webview.currentWebview().append(r),o.value&&r.hide(),r.addEventListener("click",(()=>{t("click",{},{})})),$o((()=>o.value),(e=>{r[e?"hide":"show"]()})),$o((()=>l.value),(e=>{r.setStyle(e)}),{deep:!0}),$o((()=>g.value),(()=>{r.reset(),r.draw(g.value)}),{deep:!0})})),os((()=>{r&&r.close(),i&&window.removeEventListener("updateview",i)}))}const S_=qp({name:"CoverImage",props:{src:{type:String,default:""},autoSize:{type:[Boolean,String],default:!1}},emits:["click","load","error"],setup(e,t){var{emit:n}=t,r=Qa(null),i=Zp(r,n),a=za({src:""}),o=function(e,t,n){var r,i=Qa("");function a(){t.src="",i.value=e.autoSize?"width:0;height:0;":"";var a=e.src?mc(e.src):"";0===a.indexOf("http://")||0===a.indexOf("https://")?(r=plus.downloader.createDownload(a,{filename:"_doc/uniapp_temp//download/"},((e,t)=>{200===t?o(e.filename):n("error",{},{errMsg:"error"})}))).start():a&&o(a)}function o(r){t.src=r,plus.io.getImageInfo({src:r,success:t=>{var{width:r,height:a}=t;e.autoSize&&(i.value="width:".concat(r,"px;height:").concat(a,"px;"),window.dispatchEvent(new CustomEvent("updateview"))),n("load",{},{width:r,height:a})},fail:()=>{n("error",{},{errMsg:"error"})}})}return e.src&&a(),$o((()=>e.src),a),os((()=>{r&&r.abort()})),i}(e,a,i);return x_(r,i,a),()=>vl("uni-cover-image",{ref:r,style:o.value},[vl("div",{class:"uni-cover-image"},null)],4)}});const k_=qp({name:"CoverView",emits:["click"],setup(e,t){var{emit:n}=t,r=Qa(null),i=Qa(null),a=Zp(r,n),o=za({text:""});return x_(r,a,o),$g((()=>{var e=i.value.childNodes[0];o.text=e&&e instanceof Text?e.textContent:"",window.dispatchEvent(new CustomEvent("updateview"))})),()=>vl("uni-cover-view",{ref:r},[vl("div",{ref:i,class:"uni-cover-view"},null,512)],512)}});var T_={id:{type:String,default:""},url:{type:String,default:""},mode:{type:String,default:"SD"},muted:{type:[Boolean,String],default:!1},enableCamera:{type:[Boolean,String],default:!0},autoFocus:{type:[Boolean,String],default:!0},beauty:{type:[Number,String],default:0},whiteness:{type:[Number,String],default:0},aspect:{type:[String],default:"3:2"},minBitrate:{type:[Number],default:200}},E_=["statechange","netstatus","error"];const C_=qp({name:"LivePusher",props:T_,emits:E_,setup(e,t){var n,{emit:r}=t,i=Qa(null),a=Zp(i,r),o=Qa(null),s=h_(e,["id"]),{position:l,hidden:u,onParentReady:c}=f_(o);return c((()=>{n=new plus.video.LivePusher("livePusher"+Date.now(),Object.assign({},s.value,l)),plus.webview.currentWebview().append(n),E_.forEach((e=>{n.addEventListener(e,(t=>{a(e,{},t.detail)}))})),$o((()=>s.value),(e=>n.setStyles(e)),{deep:!0}),$o((()=>l),(e=>n.setStyles(e)),{deep:!0}),$o((()=>u.value),(e=>{e||n.setStyles(l)}))})),t_(((e,t)=>{n&&n[e](t)}),r_(),!0),os((()=>{n&&n.close()})),()=>vl("uni-live-pusher",{ref:i,id:e.id},[vl("div",{ref:o,class:"uni-live-pusher-container"},null,512)],8,["id"])}});function O_(e){if(0!==e.indexOf("#"))return{color:e,opacity:1};var t=e.slice(7,9);return{color:e.slice(0,7),opacity:t?Number("0x"+t)/255:1}}const M_=qp({name:"Map",props:{id:{type:String,default:""},latitude:{type:[Number,String],default:""},longitude:{type:[Number,String],default:""},scale:{type:[String,Number],default:16},markers:{type:Array,default:()=>[]},polyline:{type:Array,default:()=>[]},circles:{type:Array,default:()=>[]},polygons:{type:Array,default:()=>[]},controls:{type:Array,default:()=>[]}},emits:["click","regionchange","controltap","markertap","callouttap"],setup(e,t){var n,{emit:r}=t,i=Qa(null),a=Zp(i,r),o=Qa(null),s=h_(e,["id"]),{position:l,hidden:u,onParentReady:c}=f_(o),{_addMarkers:d,_addMapLines:h,_addMapCircles:f,_addMapPolygons:p,_setMap:v}=function(e,t){var n;function r(t){var{longitude:r,latitude:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n&&(n.setCenter(new plus.maps.Point(Number(r||e.longitude),Number(i||e.latitude))),t({errMsg:"moveToLocation:ok"}))}function i(e){n&&n.getCurrentCenter(((t,n)=>{e({longitude:n.getLng(),latitude:n.getLat(),errMsg:"getCenterLocation:ok"})}))}function a(e){if(n){var t=n.getBounds();e({southwest:t.getSouthWest(),northeast:t.getNorthEast(),errMsg:"getRegion:ok"})}}function o(e){n&&e({scale:n.getZoom(),errMsg:"getScale:ok"})}function s(e){if(n){var{id:r,latitude:i,longitude:a,iconPath:o,callout:s,label:l}=e;(e=>{var i,{latitude:a,longitude:u}=e.coord,c=new plus.maps.Marker(new plus.maps.Point(u,a));o&&c.setIcon(mc(o)),l&&l.content&&c.setLabel(l.content);var d=void 0;s&&s.content&&(d=new plus.maps.Bubble(s.content)),d&&c.setBubble(d),(r||0===r)&&(c.onclick=e=>{t("markertap",{},{markerId:r,latitude:a,longitude:u})},d&&(d.onclick=()=>{t("callouttap",{},{markerId:r})})),null===(i=n)||void 0===i||i.addOverlay(c),n.__markers__.push(c)})({coord:{latitude:i,longitude:a}})}}function l(){n&&(n.__markers__.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),n.__markers__=[])}function u(e,t){t&&l(),e.forEach((e=>{s(e)}))}function c(e){n&&(n.__lines__.length>0&&(n.__lines__.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),n.__lines__=[]),e.forEach((e=>{var t,{color:r,width:i}=e,a=e.points.map((e=>new plus.maps.Point(e.longitude,e.latitude))),o=new plus.maps.Polyline(a);if(r){var s=O_(r);o.setStrokeColor(s.color),o.setStrokeOpacity(s.opacity)}i&&o.setLineWidth(i),null===(t=n)||void 0===t||t.addOverlay(o),n.__lines__.push(o)})))}function d(e){n&&(n.__circles__.length>0&&(n.__circles__.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),n.__circles__=[]),e.forEach((e=>{var t,{latitude:r,longitude:i,color:a,fillColor:o,radius:s,strokeWidth:l}=e,u=new plus.maps.Circle(new plus.maps.Point(i,r),s);if(a){var c=O_(a);u.setStrokeColor(c.color),u.setStrokeOpacity(c.opacity)}if(o){var d=O_(o);u.setFillColor(d.color),u.setFillOpacity(d.opacity)}l&&u.setLineWidth(l),null===(t=n)||void 0===t||t.addOverlay(u),n.__circles__.push(u)})))}function h(e){if(n){var t=n.__polygons__;t.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),t.length=0,e.forEach((e=>{var r,{points:i,strokeWidth:a,strokeColor:o,fillColor:s}=e,l=[];i&&i.forEach((e=>{l.push(new plus.maps.Point(e.longitude,e.latitude))}));var u=new plus.maps.Polygon(l);if(o){var c=O_(o);u.setStrokeColor(c.color),u.setStrokeOpacity(c.opacity)}if(s){var d=O_(s);u.setFillColor(d.color),u.setFillOpacity(d.opacity)}a&&u.setLineWidth(a),null===(r=n)||void 0===r||r.addOverlay(u),t.push(u)}))}}var f={moveToLocation:r,getCenterLocation:i,getRegion:a,getScale:o};return t_(((e,t,n)=>{f[e]&&f[e](n,t)}),r_(),!0),{_addMarkers:u,_addMapLines:c,_addMapCircles:d,_addMapPolygons:h,_setMap(e){n=e}}}(e,a);c((()=>{(n=on(plus.maps.create(Nu()+"-map-"+(e.id||Date.now()),Object.assign({},s.value,l,(()=>{if(e.latitude&&e.longitude)return{center:new plus.maps.Point(Number(e.longitude),Number(e.latitude))}})())),{__markers__:[],__lines__:[],__circles__:[],__polygons__:[]})).setZoom(parseInt(String(e.scale))),plus.webview.currentWebview().append(n),u.value&&n.hide(),n.onclick=e=>{a("tap",{},e),a("click",{},e)},n.onstatuschanged=e=>{a("regionchange",{},{})},v(n),d(e.markers),h(e.polyline),f(e.circles),p(e.polygons),$o((()=>s.value),(e=>n&&n.setStyles(e)),{deep:!0}),$o((()=>l),(e=>n&&n.setStyles(e)),{deep:!0}),$o(u,(e=>{n&&n[e?"hide":"show"]()})),$o((()=>e.scale),(e=>{n&&n.setZoom(parseInt(String(e)))})),$o([()=>e.latitude,()=>e.longitude],(e=>{var[t,r]=e;n&&n.setStyles({center:new plus.maps.Point(Number(r),Number(t))})})),$o((()=>e.markers),(e=>{d(e,!0)}),{deep:!0}),$o((()=>e.polyline),(e=>{h(e)}),{deep:!0}),$o((()=>e.circles),(e=>{f(e)}),{deep:!0}),$o((()=>e.polygons),(e=>{p(e)}),{deep:!0})}));var g=Fl((()=>e.controls.map((e=>{var t={position:"absolute"};return["top","left","width","height"].forEach((n=>{e.position[n]&&(t[n]=e.position[n]+"px")})),{id:e.id,iconPath:mc(e.iconPath),position:t,clickable:e.clickable}}))));return os((()=>{n&&(n.close(),v(null))})),()=>vl("uni-map",{ref:i,id:e.id},[vl("div",{ref:o,class:"uni-map-container"},null,512),g.value.map(((e,t)=>vl(S_,{key:t,src:e.iconPath,style:e.position,"auto-size":!0,onClick:()=>e.clickable&&a("controltap",{},{controlId:e.id})},null,8,["src","style","auto-size","onClick"]))),vl("div",{class:"uni-map-slot"},null)],8,["id"])}});var L_={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},I_={YEAR:"year",MONTH:"month",DAY:"day"};function A_(e){return e>9?e:"0".concat(e)}function B_(e,t){e=String(e||"");var n=new Date;if(t===L_.TIME){var r=e.split(":");2===r.length&&n.setHours(parseInt(r[0]),parseInt(r[1]))}else{var i=e.split("-");3===i.length&&n.setFullYear(parseInt(i[0]),parseInt(String(parseFloat(i[1])-1)),parseInt(i[2]))}return n}const N_=qp({name:"Picker",props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:L_.SELECTOR,validator:e=>Object.values(L_).indexOf(e)>=0},fields:{type:String,default:""},start:{type:String,default:function(e){if(e.mode===L_.TIME)return"00:00";if(e.mode===L_.DATE){var t=(new Date).getFullYear()-100;switch(e.fields){case I_.YEAR:return t;case I_.MONTH:return t+"-01";default:return t+"-01-01"}}return""}},end:{type:String,default:function(e){if(e.mode===L_.TIME)return"23:59";if(e.mode===L_.DATE){var t=(new Date).getFullYear()+100;switch(e.fields){case I_.YEAR:return t;case I_.MONTH:return t+"-12";default:return t+"-12-31"}}return""}},disabled:{type:[Boolean,String],default:!1}},emits:["change","cancel","columnchange"],setup(e,t){var{emit:n}=t;ci();var{t:r,getLocale:i}=li(),a=Qa(null),o=Zp(a,n),s=Qa(null),l=Qa(null),u=__uniConfig.darkmode?plus.navigator.getUIStyle():"light";function c(e){u=e.theme}UniViewJSBridge.subscribe(sr,c),os((()=>{UniViewJSBridge.unsubscribe(sr,c)}));var d=()=>{var t=e.value;switch(e.mode){case L_.MULTISELECTOR:cn(t)||(t=[]),cn(s.value)||(s.value=[]);for(var n=s.value.length=Math.max(t.length,e.range.length),r=0;r<n;r++){var i=Number(t[r]),a=Number(s.value[r]),o=isNaN(i)?isNaN(a)?0:a:i;s.value.splice(r,1,o<0?0:o)}break;case L_.TIME:case L_.DATE:s.value=String(t);break;default:var l=Number(t);s.value=l<0?0:l}},h=e=>{l.value&&l.value.sendMessage(e)},f=(t,n)=>{t.mode!==L_.TIME&&t.mode!==L_.DATE||t.fields?(t.fields=Object.values(I_).includes(t.fields)?t.fields:I_.DAY,(e=>{var t={event:"cancel"};l.value=Zu({url:"__uniapppicker",data:on({},e,{theme:u}),style:{titleNView:!1,animationType:"none",animationDuration:0,background:"rgba(0,0,0,0)",popGesture:"none"},onMessage:n=>{var r=n.event;if("created"!==r)return"columnchange"===r?(delete n.event,void o(r,{},n)):void(t=n);h(e)},onClose:()=>{l.value=null;var e=t.event;delete t.event,e&&o(e,{},t)}})})(t)):((t,n)=>{plus.nativeUI[e.mode===L_.TIME?"pickTime":"pickDate"]((t=>{var n=t.date;o("change",{},{value:e.mode===L_.TIME?"".concat(A_(n.getHours()),":").concat(A_(n.getMinutes())):"".concat(n.getFullYear(),"-").concat(A_(n.getMonth()+1),"-").concat(A_(n.getDate()))})}),(()=>{o("cancel",{},{})}),e.mode===L_.TIME?{time:B_(e.value,L_.TIME),popover:n}:{date:B_(e.value,L_.DATE),minDate:B_(e.start,L_.DATE),maxDate:B_(e.end,L_.DATE),popover:n})})(0,n)},p=t=>{if(!e.disabled){var n=t.currentTarget.getBoundingClientRect();f(Object.assign({},e,{value:s.value,locale:i(),messages:{done:r("uni.picker.done"),cancel:r("uni.picker.cancel")}}),{top:n.top+c_(),left:n.left,width:n.width,height:n.height})}},v=Ns(Jp,!1),g={submit:()=>[e.name,s.value],reset:()=>{switch(e.mode){case L_.SELECTOR:s.value=0;break;case L_.MULTISELECTOR:cn(e.value)&&(s.value=e.value.map((e=>0)));break;case L_.DATE:case L_.TIME:s.value=""}}};return v&&(v.addField(g),os((()=>v.removeField(g)))),Object.keys(e).forEach((t=>{"name"!==t&&$o((()=>e[t]),(e=>{var n={};n[t]=e,h(n)}),{deep:!0})})),$o((()=>e.value),d,{deep:!0}),d(),()=>vl("uni-picker",{ref:a,onClick:p},[vl("slot",null,null)],8,["onClick"])}});var R_={id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},vslideGesture:{type:[Boolean,String],default:!1},vslideGestureInFullscreen:{type:[Boolean,String],default:!1},showPlayBtn:{type:[Boolean,String],default:!0},showMuteBtn:{type:[Boolean,String],default:!1},enablePlayGesture:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0},showLoading:{type:[Boolean,String],default:!0},codec:{type:String,default:"hardware"},httpCache:{type:[Boolean,String],default:!1},playStrategy:{type:[Number,String],default:0},header:{type:Object,default:()=>({})},advanced:{type:Array,default:()=>[]},title:{type:String,default:""},isLive:{type:Boolean,default:!1}},P_=["play","pause","ended","timeupdate","fullscreenchange","fullscreenclick","waiting","error"],D_=["play","pause","stop","seek","sendDanmu","playbackRate","requestFullScreen","exitFullScreen"];const z_=qp({name:"Video",props:R_,emits:P_,setup(e,t){var n,{emit:r}=t,i=Qa(null),a=Zp(i,r),o=Qa(null),s=h_(e,["id"]),{position:l,hidden:u,onParentReady:c}=f_(o),d=Number(e.isLive?3:e.playStrategy);return c((()=>{n=plus.video.createVideoPlayer("video"+Date.now(),Object.assign({},s.value,l,{playStrategy:isNaN(d)?0:d})),plus.webview.currentWebview().append(n),u.value&&n.hide(),P_.forEach((e=>{n.addEventListener(e,(t=>{a(e,{},t.detail)}))})),$o((()=>s.value),(e=>n.setStyles(e)),{deep:!0}),$o((()=>l),(e=>n.setStyles(e)),{deep:!0}),$o((()=>u.value),(e=>{n[e?"hide":"show"](),e||n.setStyles(l)}))})),t_(((e,t)=>{if(D_.includes(e)){var r;switch(e){case"seek":r=t.position;break;case"sendDanmu":r=t;break;case"playbackRate":r=t.rate;break;case"requestFullScreen":r=t.direction}n&&n[e](r)}}),r_(),!0),os((()=>{n&&n.close()})),()=>vl("uni-video",{ref:i,id:e.id},[vl("div",{ref:o,class:"uni-video-container"},null,512),vl("div",{class:"uni-video-slot"},null)],8,["id"])}});var F_,$_={src:{type:String,default:""},updateTitle:{type:Boolean,default:!0},webviewStyles:{type:Object,default:()=>({})}};const j_=qp({name:"WebView",props:$_,setup(e){var t=Nu(),n=Qa(null),{hidden:r,onParentReady:i}=f_(n),a=Fl((()=>e.webviewStyles));return i((()=>{var n;(e=>{var{htmlId:t,src:n,webviewStyles:r,props:i}=e,a=plus.webview.currentWebview(),o=on({"uni-app":"none",isUniH5:!0,contentAdjust:!1},r),s=a.getTitleNView();if(s){var l=Qn+parseFloat(o.top||"0");plus.navigator.isImmersedStatusbar()&&(l+=u_()),o.top=String(l),o.bottom=o.bottom||"0"}F_=plus.webview.create(n,t,o),s&&F_.addEventListener("titleUpdate",(function(){var e;if(i.updateTitle){var t=null===(e=F_)||void 0===e?void 0:e.getTitle();a.setStyle({titleNView:{titleText:t&&"null"!==t?t:" "}})}})),plus.webview.currentWebview().append(F_)})({htmlId:Qa("webviewId"+t).value,src:mc(e.src),webviewStyles:a.value,props:e}),UniViewJSBridge.publishHandler("webviewInserted",{},t),r.value&&(null===(n=F_)||void 0===n||n.hide())})),os((()=>{var e;plus.webview.currentWebview().remove(F_),null===(e=F_)||void 0===e||e.close("none"),F_=null,UniViewJSBridge.publishHandler("webviewRemoved",{},t)})),$o((()=>e.src),(t=>{var n,r=mc(t)||"";if(r){var i;if(/^(http|https):\/\//.test(r)&&e.webviewStyles.progress)null===(i=F_)||void 0===i||i.setStyle({progress:{color:e.webviewStyles.progress.color}});null===(n=F_)||void 0===n||n.loadURL(r)}})),$o(a,(e=>{var t;null===(t=F_)||void 0===t||t.setStyle(e)})),$o(r,(e=>{F_&&F_[e?"hide":"show"]()})),()=>vl("uni-web-view",{ref:n},null,512)}});var V_={"#text":class extends gp{constructor(e,t,n,r){super(e,"#text",t,document.createTextNode("")),this._text="",this.init(r),this.insert(t,n)}init(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._text=e.t||"",t&&this.update()}setText(e){this._text=e,this.update(),this.updateView()}update(){var{space:e,decode:t}=this.$parent&&this.$parent.$props||{};this.$.textContent=Wm(this._text,{space:e,decode:t}).join(Jn)}},"#comment":class extends gp{constructor(e,t,n){super(e,"#comment",t,document.createComment("")),this.insert(t,n)}},VIEW:class extends s_{constructor(e,t,n,r){super(e,document.createElement("uni-view"),t,n,r)}},IMAGE:class extends v_{constructor(e,t,n,r){super(e,"uni-image",dg,t,n,r)}},TEXT:class extends i_{constructor(e,t,n,r){super(e,document.createElement("uni-text"),t,n,r,a_),this._text=""}init(e){this._text=e.t||"",super.init(e)}setText(e){this._text=e,this.update(),this.updateView()}update(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],{$props:{space:t,decode:n}}=this;this.$.textContent=Wm(this._text,{space:t,decode:n}).join(Jn),super.update(e)}},NAVIGATOR:class extends v_{constructor(e,t,n,r){super(e,"uni-navigator",dm,t,n,r,"uni-navigator")}},FORM:class extends v_{constructor(e,t,n,r){super(e,"uni-form",Qp,t,n,r,"span")}},BUTTON:class extends v_{constructor(e,t,n,r){super(e,"uni-button",ov,t,n,r)}},INPUT:class extends v_{constructor(e,t,n,r){super(e,"uni-input",Ig,t,n,r)}init(e){super.init(e),y_(this.$props)}},LABEL:class extends v_{constructor(e,t,n,r){super(e,"uni-label",nv,t,n,r)}},RADIO:class extends v_{constructor(e,t,n,r){super(e,"uni-radio",Em,t,n,r,".uni-radio-wrapper")}setText(e){m_(this.$holder,"uni-radio-input",e)}},CHECKBOX:class extends v_{constructor(e,t,n,r){super(e,"uni-checkbox",bv,t,n,r,".uni-checkbox-wrapper")}setText(e){m_(this.$holder,"uni-checkbox-input",e)}},"CHECKBOX-GROUP":class extends v_{constructor(e,t,n,r){super(e,"uni-checkbox-group",yv,t,n,r)}},AD:class extends v_{constructor(e,t,n,r){super(e,"uni-ad",p_,t,n,r)}},CAMERA:class extends b_{constructor(e,t,n){super(e,"uni-camera",t,n)}},CANVAS:class extends v_{constructor(e,t,n,r){super(e,"uni-canvas",mv,t,n,r,"uni-canvas > div")}},"COVER-IMAGE":class extends v_{constructor(e,t,n,r){super(e,"uni-cover-image",S_,t,n,r)}},"COVER-VIEW":class extends g_{constructor(e,t,n,r){super(e,"uni-cover-view",k_,t,n,r,".uni-cover-view")}},EDITOR:class extends v_{constructor(e,t,n,r){super(e,"uni-editor",rg,t,n,r)}},"FUNCTIONAL-PAGE-NAVIGATOR":class extends b_{constructor(e,t,n){super(e,"uni-functional-page-navigator",t,n)}},ICON:class extends v_{constructor(e,t,n,r){super(e,"uni-icon",sg,t,n,r)}},"RADIO-GROUP":class extends v_{constructor(e,t,n,r){super(e,"uni-radio-group",Tm,t,n,r)}},"LIVE-PLAYER":class extends b_{constructor(e,t,n){super(e,"uni-live-player",t,n)}},"LIVE-PUSHER":class extends v_{constructor(e,t,n,r){super(e,"uni-live-pusher",C_,t,n,r,".uni-live-pusher-slot")}},MAP:class extends v_{constructor(e,t,n,r){super(e,"uni-map",M_,t,n,r,".uni-map-slot")}},"MOVABLE-AREA":class extends g_{constructor(e,t,n,r){super(e,"uni-movable-area",jg,t,n,r)}},"MOVABLE-VIEW":class extends v_{constructor(e,t,n,r){super(e,"uni-movable-view",em,t,n,r)}},"OFFICIAL-ACCOUNT":class extends b_{constructor(e,t,n){super(e,"uni-official-account",t,n)}},"OPEN-DATA":class extends b_{constructor(e,t,n){super(e,"uni-open-data",t,n)}},PICKER:class extends v_{constructor(e,t,n,r){super(e,"uni-picker",N_,t,n,r)}},"PICKER-VIEW":class extends g_{constructor(e,t,n,r){super(e,"uni-picker-view",hm,t,n,r,".uni-picker-view-wrapper")}},"PICKER-VIEW-COLUMN":class extends g_{constructor(e,t,n,r){super(e,"uni-picker-view-column",ym,t,n,r,".uni-picker-view-content")}},PROGRESS:class extends v_{constructor(e,t,n,r){super(e,"uni-progress",xm,t,n,r)}},"RICH-TEXT":class extends v_{constructor(e,t,n,r){super(e,"uni-rich-text",Im,t,n,r)}},"SCROLL-VIEW":class extends v_{constructor(e,t,n,r){super(e,"uni-scroll-view",Nm,t,n,r,".uni-scroll-view-content")}setText(e){m_(this.$holder,"uni-scroll-view-refresher",e)}},SLIDER:class extends v_{constructor(e,t,n,r){super(e,"uni-slider",Rm,t,n,r)}},SWIPER:class extends g_{constructor(e,t,n,r){super(e,"uni-swiper",Fm,t,n,r,".uni-swiper-slide-frame")}},"SWIPER-ITEM":class extends v_{constructor(e,t,n,r){super(e,"uni-swiper-item",$m,t,n,r)}},SWITCH:class extends v_{constructor(e,t,n,r){super(e,"uni-switch",jm,t,n,r)}},TEXTAREA:class extends v_{constructor(e,t,n,r){super(e,"uni-textarea",Km,t,n,r)}init(e){super.init(e),y_(this.$props)}},VIDEO:class extends v_{constructor(e,t,n,r){super(e,"uni-video",z_,t,n,r,".uni-video-slot")}},"WEB-VIEW":class extends v_{constructor(e,t,n,r){super(e,"uni-web-view",j_,t,n,r)}}};function W_(e,t,n,r){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};if(0===e)i=new gp(e,t,n,document.createElement(t));else{var o=V_[t];i=o?new o(e,n,r,a):new Vp(e,document.createElement(t),n,r,a)}return function(e,t){ep.set(e,t)}(e,i),i}var H_=[],U_=!1;function q_(e){if(U_)return e();H_.push(e)}function Y_(){U_=!0,H_.forEach((e=>{try{e()}catch(t){console.error(t)}})),H_.length=0}function X_(e){var{css:t,route:n,platform:r,pixelRatio:i,windowWidth:a,disableScroll:o,statusbarHeight:s,windowTop:l,windowBottom:u}=e;!function(e){window.__PAGE_INFO__={route:e}}(n),function(e,t,n){window.__SYSTEM_INFO__={platform:e,pixelRatio:t,windowWidth:n}}(r,i,a),W_(0,"div",-1,-1).$=document.getElementById("app");var c=plus.webview.currentWebview().id;window.__id__=c,document.title="".concat(n,"[").concat(c,"]"),function(e,t,n){var r={"--window-left":"0px","--window-right":"0px","--window-top":t+"px","--window-bottom":n+"px","--status-bar-height":e+"px"};!function(e){var t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}(r)}(s,l,u),o&&document.addEventListener("touchmove",Ru),t?function(e){var t=document.createElement("link");t.type="text/css",t.rel="stylesheet",t.href=e+".css",t.onload=Y_,t.onerror=Y_,document.head.appendChild(t)}(n):Y_()}var Z_=!1;function G_(e,t){var{scrollTop:n,selector:r,duration:i}=e;!function(e,t,n){if(pn(e)){var r=document.querySelector(e);if(r){var{top:i}=r.getBoundingClientRect();e=i+window.pageYOffset;var a=document.querySelector("uni-page-head");a&&(e-=a.offsetHeight)}}e<0&&(e=0);var o=document.documentElement,{clientHeight:s,scrollHeight:l}=o;if(e=Math.min(e,l-s),0!==t){if(window.scrollY!==e){var u=t=>{if(t<=0)window.scrollTo(0,e);else{var n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),u(t-10)}))}};u(t)}}else o.scrollTop=document.body.scrollTop=e}(r||n||0,i),t()}function K_(e){var t=e[0];t[0]===Ir?X_(t[1]):q_((()=>function(e){var t=e[0],n=function(e){if(!e.length)return e=>e;var t=function(n){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if("number"==typeof n)return e[n];var i={};return n.forEach((e=>{var[n,a]=e;i[t(n)]=r?t(a):a})),i};return t}(t[0]===dc?t[1]:[]);e.forEach((e=>{switch(e[0]){case Ir:return X_(e[1]);case Ar:return;case Br:var t=e[3];return W_(e[1],n(e[2]),-1===t?0:t,e[4],Qf(n,e[5]));case Nr:return tp(e[1]).insert(e[2],e[3],Qf(n,e[4]));case Rr:return tp(e[1]).remove();case Pr:return tp(e[1]).setAttr(n(e[2]),n(e[3]));case Dr:return tp(e[1]).removeAttr(n(e[2]));case zr:return tp(e[1]).addEvent(n(e[2]),e[3]);case jr:return tp(e[1]).addWxsEvent(n(e[2]),n(e[3]),e[4]);case Fr:return tp(e[1]).removeEvent(n(e[2]));case $r:return tp(e[1]).setText(n(e[2]));case Vr:return function(e){if(!Z_){Z_=!0;var t={onReachBottomDistance:e,onPageScroll(e){UniViewJSBridge.publishHandler(lr,{scrollTop:e})},onReachBottom(){UniViewJSBridge.publishHandler(ur)}};requestAnimationFrame((()=>document.addEventListener("scroll",Vu(t))))}}(e[1])}})),function(){try{[...rp].sort(((e,t)=>e.priority-t.priority)).forEach((e=>e()))}finally{rp.clear()}}()}(e)))}function J_(){UniViewJSBridge.publishHandler(cc)}function Q_(e){return window.__$__(e).$}function ey(e,t){var n={},{top:r,topWindowHeight:i}=function(){var e=document.documentElement.style,t=Eu(),n=Tu(e,"--window-bottom"),r=Tu(e,"--window-left"),i=Tu(e,"--window-right"),a=Tu(e,"--top-window-height");return{top:t,bottom:n?n+Su.bottom:0,left:r?r+Su.left:0,right:i?i+Su.right:0,topWindowHeight:a||0}}();if(t.node){var a=e.tagName.replace("uni-","");a&&(n.node=e.querySelector(a))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=mr(e)),t.rect||t.size){var o=e.getBoundingClientRect();t.rect&&(n.left=o.left,n.right=o.right,n.top=o.top-r-i,n.bottom=o.bottom-r-i),t.size&&(n.width=o.width,n.height=o.height)}if(cn(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){var s=e.children[0].children[0];n.scrollLeft=s.scrollLeft,n.scrollTop=s.scrollTop,n.scrollHeight=s.scrollHeight,n.scrollWidth=s.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(cn(t.computedStyle)){var l=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=l[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function ty(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){for(var t=this.parentElement.querySelectorAll(e),n=t.length;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function ny(e,t,n,r,i){var a=function(e,t){return e?window.__$__(e).$:t.$el}(t,e),o=a.parentElement;if(!o)return r?null:[];var{nodeType:s}=a,l=3===s||8===s;if(r){var u=l?o.querySelector(n):ty(a,n)?a:a.querySelector(n);return u?ey(u,i):null}var c=[],d=(l?o:a).querySelectorAll(n);return d&&d.length&&[].forEach.call(d,(e=>{c.push(ey(e,i))})),!l&&ty(a,n)&&c.unshift(ey(a,i)),c}function ry(e,t,n){var r=[];t.forEach((t=>{var{component:n,selector:i,single:a,fields:o}=t;null===n?r.push(function(e){var t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){var n=document.documentElement,r=document.body;t.scrollLeft=n.scrollLeft||r.scrollLeft||0,t.scrollTop=n.scrollTop||r.scrollTop||0,t.scrollHeight=n.scrollHeight||r.scrollHeight||0,t.scrollWidth=n.scrollWidth||r.scrollWidth||0}return t}(o)):r.push(ny(e,n,i,a,o))})),n(r)}function iy(e,t){var{reqId:n,component:r,options:i,callback:a}=e,o=Q_(r);(o.__io||(o.__io={}))[n]=function(e,t,n){Zf();var r=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,i=new IntersectionObserver((e=>{e.forEach((e=>{n({intersectionRatio:Kf(e),intersectionRect:Gf(e.intersectionRect),boundingClientRect:Gf(e.boundingClientRect),relativeRect:Gf(e.rootBounds),time:Date.now(),dataset:mr(e.target),id:e.target.id})}))}),{root:r,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){i.USE_MUTATION_OBSERVER=!0;for(var a=e.querySelectorAll(t.selector),o=0;o<a.length;o++)i.observe(a[o])}else{i.USE_MUTATION_OBSERVER=!1;var s=e.querySelector(t.selector);s?i.observe(s):console.warn("Node ".concat(t.selector," is not found. Intersection observer will not trigger."))}return i}(o,i,a)}var ay={},oy={};function sy(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()}function ly(e,t){var{reqId:n,component:r,options:i,callback:a}=e,o=ay[n]=window.matchMedia(function(e){var t=[];for(var n of["width","minWidth","maxWidth","height","minHeight","maxHeight","orientation"])"orientation"!==n&&e[n]&&Number(e[n]>=0)&&t.push("(".concat(sy(n),": ").concat(Number(e[n]),"px)")),"orientation"===n&&e[n]&&t.push("(".concat(sy(n),": ").concat(e[n],")"));return t.join(" and ")}(i)),s=oy[n]=e=>a(e.matches);s(o),o.addListener(s)}function uy(e,t){var{family:n,source:r,desc:i}=e;(function(e,t,n){var r=document.fonts;if(r){var i=new FontFace(e,t,n);return i.load().then((()=>{r.add&&r.add(i)}))}return new Promise((r=>{var i=document.createElement("style"),a=[];if(n){var{style:o,weight:s,stretch:l,unicodeRange:u,variant:c,featureSettings:d}=n;o&&a.push("font-style:".concat(o)),s&&a.push("font-weight:".concat(s)),l&&a.push("font-stretch:".concat(l)),u&&a.push("unicode-range:".concat(u)),c&&a.push("font-variant:".concat(c)),d&&a.push("font-feature-settings:".concat(d))}i.innerText='@font-face{font-family:"'.concat(e,'";src:').concat(t,";").concat(a.join(";"),"}"),document.head.appendChild(i),r()}))})(n,r=r.startsWith('url("')||r.startsWith("url('")?"url('".concat(mc(r.substring(5,r.length-2)),"')"):r.startsWith("url(")?"url('".concat(mc(r.substring(4,r.length-1)),"')"):mc(r),i).then((()=>{t()})).catch((e=>{t(e.toString())}))}var cy={$el:document.body};function dy(){var e=Nu();!function(e,t){UniViewJSBridge.subscribe(mi(e,fi),t?t(yi):yi)}(e,(e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];q_((()=>{e.apply(null,n)}))})),_i(e,"requestComponentInfo",((e,t)=>{ry(cy,e.reqs,t)})),_i(e,"addIntersectionObserver",(e=>{iy(on({},e,{callback(t){UniViewJSBridge.publishHandler(e.eventName,t)}}))})),_i(e,"removeIntersectionObserver",(e=>{!function(e,t){var{reqId:n,component:r}=e,i=Q_(r),a=i.__io&&i.__io[n];a&&(a.disconnect(),delete i.__io[n])}(e)})),_i(e,"addMediaQueryObserver",(e=>{ly(on({},e,{callback(t){UniViewJSBridge.publishHandler(e.eventName,t)}}))})),_i(e,"removeMediaQueryObserver",(e=>{!function(e,t){var{reqId:n,component:r}=e,i=oy[n],a=ay[n];a&&(a.removeListener(i),delete oy[n],delete ay[n])}(e)})),_i(e,Xf,G_),_i(e,Yf,uy),_i(e,qf,(e=>{!function(e,t){var{pageStyle:n,rootFontSize:r}=t;n&&(document.querySelector("uni-page-body")||document.body).setAttribute("style",n),r&&document.documentElement.style.fontSize!==r&&(document.documentElement.style.fontSize=r)}(0,e)}))}function hy(){Ni(),dy(),function(){var{subscribe:e}=UniViewJSBridge;e(uc,K_),e(hc,(e=>li().setLocale(e))),e(cc,J_)}(),function(){if(0===String(navigator.vendor).indexOf("Apple")){var e,t=null;document.documentElement.addEventListener("click",(n=>{clearTimeout(e),t&&Math.abs(n.pageX-t.pageX)<=44&&Math.abs(n.pageY-t.pageY)<=44&&n.timeStamp-t.timeStamp<=450&&n.preventDefault(),t=n,e=setTimeout((()=>{t=null}),450)}))}}(),fc.publishHandler(cc)}window.uni=Jf,window.UniViewJSBridge=fc,window.rpx2px=zf,window.normalizeStyleName=Ip,window.normalizeStyleValue=_p,window.__$__=tp,window.__f__=function(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];uni.__log__?uni.__log__(e,t,...r):console[e].apply(console,[...r,t])},"undefined"!=typeof plus?hy():document.addEventListener("plusready",hy)}));
