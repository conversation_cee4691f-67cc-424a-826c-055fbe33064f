export declare const PUBLIC_DIR = "static";
export declare const EXTNAME_JS: string[];
export declare const EXTNAME_TS: string[];
export declare const EXTNAME_VUE: string[];
export declare const X_EXTNAME_VUE: string[];
export declare const EXTNAME_VUE_TEMPLATE: string[];
export declare const EXTNAME_VUE_RE: RegExp;
export declare const EXTNAME_JS_RE: RegExp;
export declare const EXTNAME_TS_RE: RegExp;
export declare const extensions: string[];
export declare const uni_app_x_extensions: string[];
export declare const PAGES_JSON_JS = "pages-json-js";
export declare const PAGES_JSON_UTS = "pages-json-uts";
export declare const MANIFEST_JSON_JS = "manifest-json-js";
export declare const MANIFEST_JSON_UTS = "manifest-json-uts";
export declare const JSON_JS_MAP: {
    readonly 'pages.json': "pages-json-js";
    readonly 'manifest.json': "manifest-json-js";
};
export declare const ASSETS_INLINE_LIMIT: number;
export declare const APP_SERVICE_FILENAME = "app-service.js";
export declare const APP_CONFIG = "app-config.js";
export declare const APP_CONFIG_SERVICE = "app-config-service.js";
export declare const BINDING_COMPONENTS = "__BINDING_COMPONENTS__";
export declare const PAGE_EXTNAME_APP: string[];
export declare const PAGE_EXTNAME: string[];
export declare const X_PAGE_EXTNAME: string[];
export declare const X_PAGE_EXTNAME_APP: string[];
export declare const H5_API_STYLE_PATH = "@dcloudio/uni-h5/style/api/";
export declare const H5_FRAMEWORK_STYLE_PATH = "@dcloudio/uni-h5/style/framework/";
export declare const H5_COMPONENTS_STYLE_PATH = "@dcloudio/uni-h5/style/";
export declare const BASE_COMPONENTS_STYLE_PATH = "@dcloudio/uni-components/style/";
export declare const X_BASE_COMPONENTS_STYLE_PATH = "@dcloudio/uni-components/style-x/";
export declare const COMMON_EXCLUDE: RegExp[];
export declare const KNOWN_ASSET_TYPES: string[];
export declare const DEFAULT_ASSETS_RE: RegExp;
export declare const TEXT_STYLE: string[];
