uni-button {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  padding-left: 14px;
  padding-right: 14px;
  box-sizing: border-box;
  font-size: 18px;
  text-align: center;
  text-decoration: none;
  line-height: 2.55555556;
  border-radius: 5px;
  -webkit-tap-highlight-color: transparent;
  overflow: hidden;
  color: #000000;
  background-color: #f8f8f8;
  cursor: pointer;
}

uni-button[hidden] {
  display: none !important;
}

uni-button:after {
  content: ' ';
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid rgba(0, 0, 0, 0.2);
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 10px;
}

uni-button[native] {
  padding-left: 0;
  padding-right: 0;
}

uni-button[native] .uni-button-cover-view-wrapper {
  border: inherit;
  border-color: inherit;
  border-radius: inherit;
  background-color: inherit;
}

uni-button[native] .uni-button-cover-view-inner {
  padding-left: 14px;
  padding-right: 14px;
}

uni-button uni-cover-view {
  line-height: inherit;
  white-space: inherit;
}

uni-button[type='default'] {
  color: #000000;
  background-color: #f8f8f8;
}

uni-button[type='primary'] {
  color: #ffffff;
  background-color: #007aff;
}

uni-button[type='warn'] {
  color: #ffffff;
  background-color: #e64340;
}

uni-button[disabled] {
  color: rgba(255, 255, 255, 0.6);
  cursor: not-allowed;
}

uni-button[disabled][type='default'],
uni-button[disabled]:not([type]) {
  color: rgba(0, 0, 0, 0.3);
  background-color: #f7f7f7;
}

uni-button[disabled][type='primary'] {
  background-color: rgba(0, 122, 255, 0.6);
}

uni-button[disabled][type='warn'] {
  background-color: #ec8b89;
}

uni-button[type='primary'][plain] {
  color: #007aff;
  border: 1px solid #007aff;
  background-color: transparent;
}

uni-button[type='primary'][plain][disabled] {
  color: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 0, 0, 0.2);
}

uni-button[type='primary'][plain]:after {
  border-width: 0;
}

uni-button[type='default'][plain] {
  color: #353535;
  border: 1px solid #353535;
  background-color: transparent;
}

uni-button[type='default'][plain][disabled] {
  color: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 0, 0, 0.2);
}

uni-button[type='default'][plain]:after {
  border-width: 0;
}

uni-button[plain] {
  color: #353535;
  border: 1px solid #353535;
  background-color: transparent;
}

uni-button[plain][disabled] {
  color: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 0, 0, 0.2);
}

uni-button[plain]:after {
  border-width: 0;
}

uni-button[plain][native] .uni-button-cover-view-inner {
  padding: 0;
}

uni-button[type='warn'][plain] {
  color: #e64340;
  border: 1px solid #e64340;
  background-color: transparent;
}

uni-button[type='warn'][plain][disabled] {
  color: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 0, 0, 0.2);
}

uni-button[type='warn'][plain]:after {
  border-width: 0;
}

uni-button[size='mini'] {
  display: inline-block;
  line-height: 2.3;
  font-size: 13px;
  padding: 0 1.34em;
}

uni-button[size='mini'][native] {
  padding: 0;
}

uni-button[size='mini'][native] .uni-button-cover-view-inner {
  padding: 0 1.34em;
}

uni-button[loading]:not([disabled]) {
  cursor: progress;
}

uni-button[loading]:before {
  content: ' ';
  display: inline-block;
  width: 18px;
  height: 18px;
  vertical-align: middle;
  animation: uni-loading 1s steps(12, end) infinite;
  background-size: 100%;
}

uni-button[loading][type='primary'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: #0062cc;
}

uni-button[loading][type='primary'][plain] {
  color: #007aff;
  background-color: transparent;
}

uni-button[loading][type='default'] {
  color: rgba(0, 0, 0, 0.6);
  background-color: #dedede;
}

uni-button[loading][type='default'][plain] {
  color: #353535;
  background-color: transparent;
}

uni-button[loading][type='warn'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: #ce3c39;
}

uni-button[loading][type='warn'][plain] {
  color: #e64340;
  background-color: transparent;
}

uni-button[loading][native]:before {
  content: none;
}

.button-hover {
  color: rgba(0, 0, 0, 0.6);
  background-color: #dedede;
}

.button-hover[plain] {
  color: rgba(53, 53, 53, 0.6);
  border-color: rgba(53, 53, 53, 0.6);
  background-color: transparent;
}

.button-hover[type='primary'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: #0062cc;
}

.button-hover[type='primary'][plain] {
  color: rgba(0, 122, 255, 0.6);
  border-color: rgba(0, 122, 255, 0.6);
  background-color: transparent;
}

.button-hover[type='default'] {
  color: rgba(0, 0, 0, 0.6);
  background-color: #dedede;
}

.button-hover[type='default'][plain] {
  color: rgba(53, 53, 53, 0.6);
  border-color: rgba(53, 53, 53, 0.6);
  background-color: transparent;
}

.button-hover[type='warn'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: #ce3c39;
}

.button-hover[type='warn'][plain] {
  color: rgba(230, 67, 64, 0.6);
  border-color: rgba(230, 67, 64, 0.6);
  background-color: transparent;
}

@media (prefers-color-scheme: dark) {
  uni-button,
  uni-button[type='default'] {
    color: #d6d6d6;
    background-color: #343434;
  }

  .button-hover,
  .button-hover[type='default'] {
    color: #d6d6d6;
    background-color: rgba(255, 255, 255, 0.1);
  }

  uni-button[disabled][type='default'],
  uni-button[disabled]:not([type]) {
    color: rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.08);
  }

  uni-button[type='primary'][plain][disabled] {
    color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
  }

  uni-button[type='default'][plain] {
    color: #d6d6d6;
    border: 1px solid #d6d6d6;
  }

  .button-hover[type='default'][plain] {
    color: rgba(150, 150, 150, 0.6);
    border-color: rgba(150, 150, 150, 0.6);
    background-color: rgba(50, 50, 50, 0.2);
  }

  uni-button[type='default'][plain][disabled] {
    border-color: hsla(0, 0%, 100%, 0.2);
    color: hsla(0, 0%, 100%, 0.2);
  }
}
