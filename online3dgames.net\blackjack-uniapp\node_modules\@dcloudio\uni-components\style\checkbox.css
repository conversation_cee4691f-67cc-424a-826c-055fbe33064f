uni-checkbox {
  -webkit-tap-highlight-color: transparent;
  display: inline-block;
  cursor: pointer;
}

uni-checkbox[hidden] {
  display: none;
}

uni-checkbox[disabled] {
  cursor: not-allowed;
}

.uni-checkbox-wrapper {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.uni-checkbox-input {
  margin-right: 5px;
  appearance: none;
  outline: 0;
  border: 1px solid #d1d1d1;
  background-color: #ffffff;
  border-radius: 3px;
  width: 22px;
  height: 22px;
  position: relative;
}

.uni-checkbox-input svg {
  color: #007aff;
  font-size: 22px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -48%) scale(0.73);
}

@media (hover: hover) {
  uni-checkbox:not([disabled]) .uni-checkbox-input:hover {
    border-color: var(--HOVER-BD-COLOR, #007aff) !important;
  }
}

.uni-checkbox-input.uni-checkbox-input-disabled {
  background-color: #e1e1e1;
}

.uni-checkbox-input.uni-checkbox-input-disabled:before {
  color: #adadad;
}

uni-checkbox-group {
  display: block;
}
