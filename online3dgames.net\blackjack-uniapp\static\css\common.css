/* 通用样式 */
* {
	box-sizing: border-box;
}

page {
	background: #1a4b3a;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 卡牌样式 */
.card {
	width: 120rpx;
	height: 168rpx;
	background: #ffffff;
	border-radius: 12rpx;
	border: 2rpx solid #ddd;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	padding: 8rpx;
	position: relative;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
	margin: 4rpx;
}

.card.red {
	color: #dc2626;
}

.card.black {
	color: #1f2937;
}

.card-back {
	background: linear-gradient(145deg, #1e40af, #3b82f6);
	position: relative;
	overflow: hidden;
}

.card-back::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	width: 80rpx;
	height: 80rpx;
	background: repeating-linear-gradient(45deg,
		rgba(255, 255, 255, 0.2),
		rgba(255, 255, 255, 0.2) 4rpx,
		transparent 4rpx,
		transparent 12rpx);
	transform: translate(-50%, -50%);
	border-radius: 8rpx;
}

.card-value {
	font-size: 24rpx;
	line-height: 1;
	font-weight: 600;
}

.card-suit {
	font-size: 20rpx;
	text-align: center;
}

/* 筹码样式 */
.chip {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	font-size: 24rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
	position: relative;
	margin: 4rpx;
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
}

.chip-1 {
	background-image: url('/static/images/chips/chips1.png');
}

.chip-5 {
	background-image: url('/static/images/chips/chips2.png');
}

.chip-10 {
	background-image: url('/static/images/chips/chips3.png');
}

.chip-25 {
	background-image: url('/static/images/chips/chips4.png');
}

.chip-50 {
	background-image: url('/static/images/chips/chips5.png');
}

.chip-100 {
	background-image: url('/static/images/chips/chips6.png');
}

.chip-500 {
	background-image: url('/static/images/chips/chips7.png');
}

/* 按钮样式 */
.game-btn {
	height: 80rpx;
	border-radius: 12rpx;
	border: none;
	font-size: 32rpx;
	font-weight: bold;
	color: #ffffff;
	background: linear-gradient(135deg, #16a34a, #15803d);
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
	margin: 8rpx;
	padding: 0 32rpx;
}

.game-btn:disabled {
	background: #6b7280;
	opacity: 0.5;
}

.game-btn.danger {
	background: linear-gradient(135deg, #dc2626, #b91c1c);
}

.game-btn.warning {
	background: linear-gradient(135deg, #f59e0b, #d97706);
}

.game-btn.primary {
	background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

/* 动画 */
@keyframes cardDeal {
	0% {
		transform: translateY(-200rpx) rotateY(180deg);
		opacity: 0;
	}
	50% {
		transform: translateY(-40rpx) rotateY(90deg);
		opacity: 0.5;
	}
	100% {
		transform: translateY(0) rotateY(0deg);
		opacity: 1;
	}
}

@keyframes cardFlip {
	0% {
		transform: rotateY(0deg);
	}
	50% {
		transform: rotateY(90deg);
	}
	100% {
		transform: rotateY(0deg);
	}
}

@keyframes chipFly {
	0% {
		transform: scale(1) translateY(0);
		opacity: 1;
	}
	50% {
		transform: scale(0.8) translateY(-100rpx);
		opacity: 0.8;
	}
	100% {
		transform: scale(1) translateY(0);
		opacity: 1;
	}
}

@keyframes pulse {
	0%, 100% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.05);
		opacity: 0.8;
	}
}

@keyframes shake {
	0%, 100% {
		transform: translateX(0);
	}
	25% {
		transform: translateX(-10rpx);
	}
	75% {
		transform: translateX(10rpx);
	}
}

/* 游戏效果 */
.blackjack-effect {
	animation: pulse 0.6s ease-in-out;
	background: linear-gradient(135deg, #ffd700, #ffed4e);
	color: #1a4b3a;
}

.bust-effect {
	animation: shake 0.5s ease-in-out;
	background: linear-gradient(135deg, #dc2626, #b91c1c);
	color: #ffffff;
}

.win-effect {
	animation: pulse 0.8s ease-in-out;
	background: linear-gradient(135deg, #16a34a, #15803d);
	color: #ffffff;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.card {
		width: 100rpx;
		height: 140rpx;
	}
	
	.chip {
		width: 60rpx;
		height: 60rpx;
		font-size: 20rpx;
	}
	
	.game-btn {
		height: 70rpx;
		font-size: 28rpx;
	}
}
