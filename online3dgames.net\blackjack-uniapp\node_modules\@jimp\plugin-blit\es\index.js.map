{"version": 3, "sources": ["../src/index.js"], "names": ["blit", "src", "x", "y", "srcx", "srcy", "srcw", "srch", "cb", "constructor", "throwError", "call", "bitmap", "width", "height", "Math", "round", "max<PERSON><PERSON><PERSON>", "maxHeight", "baseImage", "scanQuiet", "sx", "sy", "idx", "xOffset", "yOffset", "dstIdx", "getPixelIndex", "r", "data", "g", "b", "a", "dst", "limit255"], "mappings": ";;;;;;;;;;;AAAA;;eAEe;AAAA,SAAO;AACpB;;;;;;;;;;;;AAYAA,IAAAA,IAboB,gBAafC,GAbe,EAaVC,CAbU,EAaPC,CAbO,EAaJC,IAbI,EAaEC,IAbF,EAaQC,IAbR,EAacC,IAbd,EAaoBC,EAbpB,EAawB;AAC1C,UAAI,EAAEP,GAAG,YAAY,KAAKQ,WAAtB,CAAJ,EAAwC;AACtC,eAAOC,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,iCAAtB,EAAyDH,EAAzD,CAAP;AACD;;AAED,UAAI,OAAON,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,eAAOO,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDH,EAAjD,CAAP;AACD;;AAED,UAAI,OAAOJ,IAAP,KAAgB,UAApB,EAAgC;AAC9BI,QAAAA,EAAE,GAAGJ,IAAL;AACAA,QAAAA,IAAI,GAAG,CAAP;AACAC,QAAAA,IAAI,GAAG,CAAP;AACAC,QAAAA,IAAI,GAAGL,GAAG,CAACW,MAAJ,CAAWC,KAAlB;AACAN,QAAAA,IAAI,GAAGN,GAAG,CAACW,MAAJ,CAAWE,MAAlB;AACD,OAND,MAMO,IACL,yBAAOV,IAAP,+BAAuBC,IAAvB,KACA,yBAAOA,IAAP,+BAAuBC,IAAvB,CADA,IAEA,yBAAOA,IAAP,+BAAuBC,IAAvB,CAHK,EAIL;AACAH,QAAAA,IAAI,GAAGA,IAAI,IAAI,CAAf;AACAC,QAAAA,IAAI,GAAGA,IAAI,IAAI,CAAf;AACAC,QAAAA,IAAI,GAAGA,IAAI,IAAIL,GAAG,CAACW,MAAJ,CAAWC,KAA1B;AACAN,QAAAA,IAAI,GAAGA,IAAI,IAAIN,GAAG,CAACW,MAAJ,CAAWE,MAA1B;AACD,OATM,MASA;AACL,eAAOJ,kBAAWC,IAAX,CACL,IADK,EAEL,wCAFK,EAGLH,EAHK,CAAP;AAKD,OA9ByC,CAgC1C;;;AACAN,MAAAA,CAAC,GAAGa,IAAI,CAACC,KAAL,CAAWd,CAAX,CAAJ;AACAC,MAAAA,CAAC,GAAGY,IAAI,CAACC,KAAL,CAAWb,CAAX,CAAJ,CAlC0C,CAoC1C;;AACAC,MAAAA,IAAI,GAAGW,IAAI,CAACC,KAAL,CAAWZ,IAAX,CAAP;AACAC,MAAAA,IAAI,GAAGU,IAAI,CAACC,KAAL,CAAWX,IAAX,CAAP;AACAC,MAAAA,IAAI,GAAGS,IAAI,CAACC,KAAL,CAAWV,IAAX,CAAP;AACAC,MAAAA,IAAI,GAAGQ,IAAI,CAACC,KAAL,CAAWT,IAAX,CAAP;AAEA,UAAMU,QAAQ,GAAG,KAAKL,MAAL,CAAYC,KAA7B;AACA,UAAMK,SAAS,GAAG,KAAKN,MAAL,CAAYE,MAA9B;AACA,UAAMK,SAAS,GAAG,IAAlB;AAEAlB,MAAAA,GAAG,CAACmB,SAAJ,CAAchB,IAAd,EAAoBC,IAApB,EAA0BC,IAA1B,EAAgCC,IAAhC,EAAsC,UAASc,EAAT,EAAaC,EAAb,EAAiBC,GAAjB,EAAsB;AAC1D,YAAMC,OAAO,GAAGtB,CAAC,GAAGmB,EAAJ,GAASjB,IAAzB;AACA,YAAMqB,OAAO,GAAGtB,CAAC,GAAGmB,EAAJ,GAASjB,IAAzB;;AAEA,YACEmB,OAAO,IAAI,CAAX,IACAC,OAAO,IAAI,CADX,IAEAR,QAAQ,GAAGO,OAAX,GAAqB,CAFrB,IAGAN,SAAS,GAAGO,OAAZ,GAAsB,CAJxB,EAKE;AACA,cAAMC,MAAM,GAAGP,SAAS,CAACQ,aAAV,CAAwBH,OAAxB,EAAiCC,OAAjC,CAAf;AACA,cAAMxB,IAAG,GAAG;AACV2B,YAAAA,CAAC,EAAE,KAAKhB,MAAL,CAAYiB,IAAZ,CAAiBN,GAAjB,CADO;AAEVO,YAAAA,CAAC,EAAE,KAAKlB,MAAL,CAAYiB,IAAZ,CAAiBN,GAAG,GAAG,CAAvB,CAFO;AAGVQ,YAAAA,CAAC,EAAE,KAAKnB,MAAL,CAAYiB,IAAZ,CAAiBN,GAAG,GAAG,CAAvB,CAHO;AAIVS,YAAAA,CAAC,EAAE,KAAKpB,MAAL,CAAYiB,IAAZ,CAAiBN,GAAG,GAAG,CAAvB;AAJO,WAAZ;AAOA,cAAMU,GAAG,GAAG;AACVL,YAAAA,CAAC,EAAET,SAAS,CAACP,MAAV,CAAiBiB,IAAjB,CAAsBH,MAAtB,CADO;AAEVI,YAAAA,CAAC,EAAEX,SAAS,CAACP,MAAV,CAAiBiB,IAAjB,CAAsBH,MAAM,GAAG,CAA/B,CAFO;AAGVK,YAAAA,CAAC,EAAEZ,SAAS,CAACP,MAAV,CAAiBiB,IAAjB,CAAsBH,MAAM,GAAG,CAA/B,CAHO;AAIVM,YAAAA,CAAC,EAAEb,SAAS,CAACP,MAAV,CAAiBiB,IAAjB,CAAsBH,MAAM,GAAG,CAA/B;AAJO,WAAZ;AAOAP,UAAAA,SAAS,CAACP,MAAV,CAAiBiB,IAAjB,CAAsBH,MAAtB,IACE,CAAEzB,IAAG,CAAC+B,CAAJ,IAAS/B,IAAG,CAAC2B,CAAJ,GAAQK,GAAG,CAACL,CAArB,IAA0BK,GAAG,CAACL,CAA9B,GAAkC,GAAnC,IAA2C,CAA5C,IAAiDK,GAAG,CAACL,CADvD;AAEAT,UAAAA,SAAS,CAACP,MAAV,CAAiBiB,IAAjB,CAAsBH,MAAM,GAAG,CAA/B,IACE,CAAEzB,IAAG,CAAC+B,CAAJ,IAAS/B,IAAG,CAAC6B,CAAJ,GAAQG,GAAG,CAACH,CAArB,IAA0BG,GAAG,CAACH,CAA9B,GAAkC,GAAnC,IAA2C,CAA5C,IAAiDG,GAAG,CAACH,CADvD;AAEAX,UAAAA,SAAS,CAACP,MAAV,CAAiBiB,IAAjB,CAAsBH,MAAM,GAAG,CAA/B,IACE,CAAEzB,IAAG,CAAC+B,CAAJ,IAAS/B,IAAG,CAAC8B,CAAJ,GAAQE,GAAG,CAACF,CAArB,IAA0BE,GAAG,CAACF,CAA9B,GAAkC,GAAnC,IAA2C,CAA5C,IAAiDE,GAAG,CAACF,CADvD;AAEAZ,UAAAA,SAAS,CAACP,MAAV,CAAiBiB,IAAjB,CAAsBH,MAAM,GAAG,CAA/B,IAAoC,KAAKjB,WAAL,CAAiByB,QAAjB,CAClCD,GAAG,CAACD,CAAJ,GAAQ/B,IAAG,CAAC+B,CADsB,CAApC;AAGD;AACF,OAnCD;;AAqCA,UAAI,0BAAcxB,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACG,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AArGmB,GAAP;AAAA,C", "sourcesContent": ["import { throwError, isNodePattern } from '@jimp/utils';\n\nexport default () => ({\n  /**\n   * Blits a source image on to this image\n   * @param {Jimp} src the source Jimp instance\n   * @param {number} x the x position to blit the image\n   * @param {number} y the y position to blit the image\n   * @param {number} srcx (optional) the x position from which to crop the source image\n   * @param {number} srcy (optional) the y position from which to crop the source image\n   * @param {number} srcw (optional) the width to which to crop the source image\n   * @param {number} srch (optional) the height to which to crop the source image\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  blit(src, x, y, srcx, srcy, srcw, srch, cb) {\n    if (!(src instanceof this.constructor)) {\n      return throwError.call(this, 'The source must be a Jimp image', cb);\n    }\n\n    if (typeof x !== 'number' || typeof y !== 'number') {\n      return throwError.call(this, 'x and y must be numbers', cb);\n    }\n\n    if (typeof srcx === 'function') {\n      cb = srcx;\n      srcx = 0;\n      srcy = 0;\n      srcw = src.bitmap.width;\n      srch = src.bitmap.height;\n    } else if (\n      typeof srcx === typeof srcy &&\n      typeof srcy === typeof srcw &&\n      typeof srcw === typeof srch\n    ) {\n      srcx = srcx || 0;\n      srcy = srcy || 0;\n      srcw = srcw || src.bitmap.width;\n      srch = srch || src.bitmap.height;\n    } else {\n      return throwError.call(\n        this,\n        'srcx, srcy, srcw, srch must be numbers',\n        cb\n      );\n    }\n\n    // round input\n    x = Math.round(x);\n    y = Math.round(y);\n\n    // round input\n    srcx = Math.round(srcx);\n    srcy = Math.round(srcy);\n    srcw = Math.round(srcw);\n    srch = Math.round(srch);\n\n    const maxWidth = this.bitmap.width;\n    const maxHeight = this.bitmap.height;\n    const baseImage = this;\n\n    src.scanQuiet(srcx, srcy, srcw, srch, function(sx, sy, idx) {\n      const xOffset = x + sx - srcx;\n      const yOffset = y + sy - srcy;\n\n      if (\n        xOffset >= 0 &&\n        yOffset >= 0 &&\n        maxWidth - xOffset > 0 &&\n        maxHeight - yOffset > 0\n      ) {\n        const dstIdx = baseImage.getPixelIndex(xOffset, yOffset);\n        const src = {\n          r: this.bitmap.data[idx],\n          g: this.bitmap.data[idx + 1],\n          b: this.bitmap.data[idx + 2],\n          a: this.bitmap.data[idx + 3]\n        };\n\n        const dst = {\n          r: baseImage.bitmap.data[dstIdx],\n          g: baseImage.bitmap.data[dstIdx + 1],\n          b: baseImage.bitmap.data[dstIdx + 2],\n          a: baseImage.bitmap.data[dstIdx + 3]\n        };\n\n        baseImage.bitmap.data[dstIdx] =\n          ((src.a * (src.r - dst.r) - dst.r + 255) >> 8) + dst.r;\n        baseImage.bitmap.data[dstIdx + 1] =\n          ((src.a * (src.g - dst.g) - dst.g + 255) >> 8) + dst.g;\n        baseImage.bitmap.data[dstIdx + 2] =\n          ((src.a * (src.b - dst.b) - dst.b + 255) >> 8) + dst.b;\n        baseImage.bitmap.data[dstIdx + 3] = this.constructor.limit255(\n          dst.a + src.a\n        );\n      }\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}